"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AcademicDashboard.tsx":
/*!**********************************************!*\
  !*** ./src/components/AcademicDashboard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isBefore.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst AcademicDashboard = ()=>{\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notices, setNotices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedule, setSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exams, setExams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [emails, setEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [syllabus, setSyllabus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Update time every second for real-time countdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"AcademicDashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"AcademicDashboard.useEffect.timer\"], 1000);\n            return ({\n                \"AcademicDashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"AcademicDashboard.useEffect\"];\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    // Mock data - In real implementation, this would come from APIs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            // Sample assignments based on your courses with auto-detected deadlines\n            const sampleAssignments = [\n                {\n                    id: '1',\n                    title: 'Programming Assignment 4',\n                    subject: 'CSE340',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 3),\n                    description: 'Object-oriented programming concepts implementation',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '2',\n                    title: 'Database Lab Report',\n                    subject: 'CSE321',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 5),\n                    description: 'Complete ER diagram and SQL queries',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'slack'\n                },\n                {\n                    id: '3',\n                    title: 'Algorithm Analysis Project',\n                    subject: 'CSE460',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 7),\n                    description: 'Time complexity analysis of sorting algorithms',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'discord'\n                },\n                {\n                    id: '4',\n                    title: 'Microprocessor Lab Assignment',\n                    subject: 'CST204',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 4),\n                    description: 'Assembly language programming task',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                }\n            ];\n            const sampleNotices = [\n                {\n                    id: '1',\n                    title: 'Mid-term Exam Schedule Released',\n                    content: 'Check your email for detailed exam timetable',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '2',\n                    title: 'Lab Session Rescheduled',\n                    content: 'CSE 301 lab moved to Friday 2 PM',\n                    date: new Date(),\n                    source: 'slack',\n                    priority: 'medium'\n                }\n            ];\n            // Your actual class schedule\n            const sampleSchedule = [\n                // Sunday\n                {\n                    id: '1',\n                    subject: 'Programming',\n                    courseCode: 'CSE340',\n                    time: '8:30 AM - 9:20 AM',\n                    room: 'PBK-03A-OBC',\n                    day: 'Sunday'\n                },\n                // Monday\n                {\n                    id: '2',\n                    subject: 'Programming',\n                    courseCode: 'CSE340',\n                    time: '8:30 AM - 9:20 AM',\n                    room: 'PBK-03A-OBC',\n                    day: 'Monday'\n                },\n                // Tuesday\n                {\n                    id: '3',\n                    subject: 'Programming',\n                    courseCode: 'CSE340',\n                    time: '8:30 AM - 9:20 AM',\n                    room: 'PBK-03A-OBC',\n                    day: 'Tuesday'\n                },\n                // Wednesday\n                {\n                    id: '4',\n                    subject: 'Object Oriented Programming',\n                    courseCode: 'CSE340L',\n                    time: '9:00 AM - 10:50 AM',\n                    room: 'TBA-03P-24L',\n                    day: 'Wednesday'\n                },\n                {\n                    id: '5',\n                    subject: 'Algorithms',\n                    courseCode: 'CSE321L',\n                    time: '11:00 AM - 1:50 PM',\n                    room: 'TBA-03P-25L',\n                    day: 'Wednesday'\n                },\n                // Thursday\n                {\n                    id: '6',\n                    subject: 'Algorithms',\n                    courseCode: 'CSE460',\n                    time: '11:00 AM - 12:20 PM',\n                    room: '1MF-03D-17C',\n                    day: 'Thursday'\n                },\n                {\n                    id: '7',\n                    subject: 'Microprocessor',\n                    courseCode: 'CST204',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '01-MFC-09E-23C',\n                    day: 'Thursday'\n                },\n                // Friday\n                {\n                    id: '8',\n                    subject: 'Algorithms',\n                    courseCode: 'CSE460',\n                    time: '11:00 AM - 12:20 PM',\n                    room: '1AP-06D-17C',\n                    day: 'Friday'\n                },\n                {\n                    id: '9',\n                    subject: 'Microprocessor',\n                    courseCode: 'CST204',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '01-MFC-09E-23C',\n                    day: 'Friday'\n                },\n                // Saturday\n                {\n                    id: '10',\n                    subject: 'Database Lab',\n                    courseCode: 'CSE321',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '14-ZMD-03D-18C',\n                    day: 'Saturday'\n                }\n            ];\n            // Your actual exam schedule\n            const sampleExams = [\n                {\n                    id: '1',\n                    date: '2025-07-26',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE460',\n                    courseName: 'Algorithms'\n                },\n                {\n                    id: '2',\n                    date: '2025-07-31',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE321',\n                    courseName: 'Database'\n                },\n                {\n                    id: '3',\n                    date: '2025-08-02',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE340',\n                    courseName: 'Programming'\n                },\n                {\n                    id: '4',\n                    date: '2025-08-03',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CST204',\n                    courseName: 'Microprocessor'\n                },\n                {\n                    id: '5',\n                    date: '2025-08-14',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE460',\n                    courseName: 'Algorithms'\n                },\n                {\n                    id: '6',\n                    date: '2025-08-19',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE321',\n                    courseName: 'Database'\n                },\n                {\n                    id: '7',\n                    date: '2025-08-20',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE340',\n                    courseName: 'Programming'\n                },\n                {\n                    id: '8',\n                    date: '2025-08-21',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CST204',\n                    courseName: 'Microprocessor'\n                }\n            ];\n            const sampleEmails = [\n                {\n                    id: '1',\n                    subject: 'Assignment Submission Reminder',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                },\n                {\n                    id: '2',\n                    subject: 'Course Material Updated',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                }\n            ];\n            const sampleSyllabus = [\n                {\n                    id: '1',\n                    subject: 'CSE 201',\n                    topic: 'Binary Trees and BST',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '2',\n                    subject: 'CSE 301',\n                    topic: 'Database Normalization',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '3',\n                    subject: 'CSE 401',\n                    topic: 'Dynamic Programming',\n                    week: 9,\n                    status: 'upcoming'\n                }\n            ];\n            setAssignments(sampleAssignments);\n            setNotices(sampleNotices);\n            setSchedule(sampleSchedule);\n            setEmails(sampleEmails);\n            setSyllabus(sampleSyllabus);\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    const getTimeUntilDeadline = (dueDate)=>{\n        const now = new Date();\n        if ((0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__.isBefore)(dueDate, now)) {\n            return 'OVERDUE';\n        }\n        return (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(dueDate, {\n            addSuffix: true\n        });\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50 border-red-200';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n            case 'low':\n                return 'text-green-600 bg-green-50 border-green-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    const getSourceIcon = (source)=>{\n        switch(source){\n            case 'classroom':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 32\n                }, undefined);\n            case 'slack':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 28\n                }, undefined);\n            case 'discord':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 30\n                }, undefined);\n            case 'email':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 28\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4\",\n        style: {\n            width: '1024px',\n            height: '600px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-4 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-800\",\n                                    children: \"Academic Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"CSE Student Portal\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-semibold text-gray-800\",\n                                    children: currentTime.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: currentTime.toLocaleDateString('en-US', {\n                                        weekday: 'long',\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4 h-[calc(100%-120px)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Deadlines\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: assignments.map((assignment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(assignment.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    getSourceIcon(assignment.source),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 font-medium text-sm\",\n                                                                        children: assignment.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-bold\",\n                                                                children: getTimeUntilDeadline(assignment.dueDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-sm mb-1\",\n                                                        children: assignment.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: assignment.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, assignment.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-48 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Today's Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: schedule.map((class_item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-50 rounded-lg border border-blue-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: class_item.subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: class_item.instructor\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-semibold text-blue-600\",\n                                                                    children: class_item.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: class_item.room\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, class_item.id, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Recent Notices\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: notices.map((notice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(notice.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getSourceIcon(notice.source),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-xs text-gray-500\",\n                                                                    children: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(notice.date, {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-sm mb-1\",\n                                                        children: notice.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: notice.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, notice.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-48 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Current Syllabus\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: syllabus.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-50 rounded-lg border border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: item.subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: item.topic\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 font-semibold\",\n                                                                    children: [\n                                                                        \"Week \",\n                                                                        item.week\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs px-2 py-1 rounded \".concat(item.status === 'current' ? 'bg-green-200 text-green-800' : item.status === 'completed' ? 'bg-gray-200 text-gray-800' : 'bg-blue-200 text-blue-800'),\n                                                                    children: item.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, item.id, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 text-purple-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Recent Emails\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: emails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg border \".concat(email.isRead ? 'bg-gray-50 border-gray-200' : 'bg-purple-50 border-purple-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-sm truncate\",\n                                                                children: email.subject\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !email.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 39\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600 truncate\",\n                                                        children: email.sender\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(email.date, {\n                                                            addSuffix: true\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, email.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-48\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5 text-indigo-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-3 rounded-lg border border-red-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: assignments.length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-red-600\",\n                                                        children: \"Pending Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: schedule.length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-600\",\n                                                        children: \"Today's Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-50 p-3 rounded-lg border border-purple-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: emails.filter((e)=>!e.isRead).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-purple-600\",\n                                                        children: \"Unread Emails\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-3 rounded-lg border border-green-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: syllabus.filter((s)=>s.status === 'current').length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-600\",\n                                                        children: \"Current Topics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AcademicDashboard, \"QqYYjitf6Dwya1H9wXNNpC4jF60=\");\n_c = AcademicDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AcademicDashboard);\nvar _c;\n$RefreshReg$(_c, \"AcademicDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AcademicDashboard.tsx\n"));

/***/ })

});