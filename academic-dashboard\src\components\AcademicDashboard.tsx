'use client';

import React, { useState, useEffect } from 'react';
import { Clock, Calendar, BookOpen, Mail, Bell, AlertTriangle, CheckCircle, Users, FileText, GraduationCap } from 'lucide-react';
import { formatDistanceToNow, isAfter, isBefore, addDays } from 'date-fns';

// Types for our data structures
interface Assignment {
  id: string;
  title: string;
  subject: string;
  dueDate: Date;
  description: string;
  priority: 'high' | 'medium' | 'low';
  status: 'pending' | 'submitted' | 'overdue';
  source: 'classroom' | 'slack' | 'discord';
}

interface Notice {
  id: string;
  title: string;
  content: string;
  date: Date;
  source: 'classroom' | 'slack' | 'discord' | 'email';
  priority: 'high' | 'medium' | 'low';
}

interface ClassSchedule {
  id: string;
  subject: string;
  courseCode: string;
  section: string;
  faculty: string;
  time: string;
  room: string;
  day: string;
  duration: string;
}

interface ExamSchedule {
  id: string;
  date: string;
  time: string;
  examType: 'MID' | 'FINAL';
  courseCode: string;
  courseName: string;
}

interface Email {
  id: string;
  subject: string;
  sender: string;
  date: Date;
  isRead: boolean;
}

interface SyllabusItem {
  id: string;
  subject: string;
  topic: string;
  week: number;
  status: 'completed' | 'current' | 'upcoming';
}

const AcademicDashboard: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [notices, setNotices] = useState<Notice[]>([]);
  const [schedule, setSchedule] = useState<ClassSchedule[]>([]);
  const [exams, setExams] = useState<ExamSchedule[]>([]);
  const [emails, setEmails] = useState<Email[]>([]);
  const [syllabus, setSyllabus] = useState<SyllabusItem[]>([]);

  // Update time every second for real-time countdown
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Mock data - In real implementation, this would come from APIs
  useEffect(() => {
    // Sample assignments based on your actual courses with auto-detected deadlines
    const sampleAssignments: Assignment[] = [
      {
        id: '1',
        title: 'Computer Architecture Assignment 3',
        subject: 'CSE340',
        dueDate: addDays(new Date(), 3),
        description: 'CPU design and instruction set architecture analysis',
        priority: 'high',
        status: 'pending',
        source: 'classroom'
      },
      {
        id: '2',
        title: 'Operating Systems Lab Report',
        subject: 'CSE321',
        dueDate: addDays(new Date(), 5),
        description: 'Process scheduling and memory management implementation',
        priority: 'high',
        status: 'pending',
        source: 'slack'
      },
      {
        id: '3',
        title: 'VLSI Design Project Phase 2',
        subject: 'CSE460',
        dueDate: addDays(new Date(), 7),
        description: 'Digital circuit design using Verilog HDL',
        priority: 'medium',
        status: 'pending',
        source: 'discord'
      },
      {
        id: '4',
        title: 'Final Year Project Proposal',
        subject: 'CSE400',
        dueDate: addDays(new Date(), 10),
        description: 'Complete project proposal and literature review',
        priority: 'high',
        status: 'pending',
        source: 'classroom'
      },
      {
        id: '5',
        title: 'Communication Research Paper',
        subject: 'CST204',
        dueDate: addDays(new Date(), 4),
        description: 'Social change communication strategies analysis',
        priority: 'medium',
        status: 'pending',
        source: 'classroom'
      }
    ];

    const sampleNotices: Notice[] = [
      {
        id: '1',
        title: 'Mid-term Exam Schedule Released',
        content: 'Check your email for detailed exam timetable. First exam: VLSI Design on July 26',
        date: new Date(),
        source: 'classroom',
        priority: 'high'
      },
      {
        id: '2',
        title: 'Operating Systems Lab Rescheduled',
        content: 'CSE321L lab session moved to Wednesday 11:00 AM - 1:50 PM',
        date: new Date(),
        source: 'slack',
        priority: 'medium'
      },
      {
        id: '3',
        title: 'Final Year Project Proposal Deadline',
        content: 'CSE400 project proposals due next week. Submit to course coordinator.',
        date: new Date(),
        source: 'discord',
        priority: 'high'
      },
      {
        id: '4',
        title: 'VLSI Design Software Update',
        content: 'New Verilog simulation tools available in lab. Training session tomorrow.',
        date: new Date(),
        source: 'classroom',
        priority: 'medium'
      }
    ];

    // Your actual class schedule with proper format (Section-Faculty-Room)
    const sampleSchedule: ClassSchedule[] = [
      // Sunday
      {
        id: '1',
        subject: 'Computer Architecture',
        courseCode: 'CSE340',
        section: '08',
        faculty: 'PBK',
        time: '8:30 AM - 9:20 AM',
        room: '03A-OBC',
        day: 'Sunday',
        duration: '50 mins'
      },

      // Monday
      {
        id: '2',
        subject: 'Computer Architecture',
        courseCode: 'CSE340',
        section: '08',
        faculty: 'PBK',
        time: '8:30 AM - 9:20 AM',
        room: '03A-OBC',
        day: 'Monday',
        duration: '50 mins'
      },

      // Tuesday
      {
        id: '3',
        subject: 'Computer Architecture',
        courseCode: 'CSE340',
        section: '08',
        faculty: 'PBK',
        time: '8:30 AM - 9:20 AM',
        room: '03A-OBC',
        day: 'Tuesday',
        duration: '50 mins'
      },

      // Wednesday
      {
        id: '4',
        subject: 'Computer Architecture Lab',
        courseCode: 'CSE340L',
        section: '06',
        faculty: 'TBA',
        time: '9:00 AM - 10:50 AM',
        room: '03P-24L',
        day: 'Wednesday',
        duration: '1 hr 50 mins'
      },
      {
        id: '5',
        subject: 'Operating Systems Lab',
        courseCode: 'CSE321L',
        section: '14',
        faculty: 'TBA',
        time: '11:00 AM - 1:50 PM',
        room: '03P-25L',
        day: 'Wednesday',
        duration: '2 hrs 50 mins'
      },

      // Thursday
      {
        id: '6',
        subject: 'VLSI Design',
        courseCode: 'CSE460',
        section: '06',
        faculty: '1MF',
        time: '11:00 AM - 12:20 PM',
        room: '03D-17C',
        day: 'Thursday',
        duration: '1 hr 20 mins'
      },
      {
        id: '7',
        subject: 'Communication for Social Change',
        courseCode: 'CST204',
        section: '01',
        faculty: 'MFC',
        time: '12:30 PM - 1:50 PM',
        room: '09E-23C',
        day: 'Thursday',
        duration: '1 hr 20 mins'
      },

      // Friday
      {
        id: '8',
        subject: 'VLSI Design',
        courseCode: 'CSE460',
        section: '06',
        faculty: '1AP',
        time: '11:00 AM - 12:20 PM',
        room: '06D-17C',
        day: 'Friday',
        duration: '1 hr 20 mins'
      },
      {
        id: '9',
        subject: 'Communication for Social Change',
        courseCode: 'CST204',
        section: '01',
        faculty: 'MFC',
        time: '12:30 PM - 1:50 PM',
        room: '09E-23C',
        day: 'Friday',
        duration: '1 hr 20 mins'
      },

      // Saturday
      {
        id: '10',
        subject: 'Operating Systems',
        courseCode: 'CSE321',
        section: '14',
        faculty: 'ZMD',
        time: '12:30 PM - 1:50 PM',
        room: '03D-18C',
        day: 'Saturday',
        duration: '1 hr 20 mins'
      },

      // VLSI Design Lab (you mentioned)
      {
        id: '11',
        subject: 'VLSI Design Lab',
        courseCode: 'CSE460L',
        section: '06',
        faculty: 'TBA',
        time: '8:00 AM - 10:50 AM',
        room: '09F-24L',
        day: 'Wednesday',
        duration: '2 hrs 50 mins'
      },
    ];

    // Your actual exam schedule with correct course names
    const sampleExams: ExamSchedule[] = [
      { id: '1', date: '2025-07-26', time: '4:30 PM - 6:30 PM', examType: 'MID', courseCode: 'CSE460', courseName: 'VLSI Design' },
      { id: '2', date: '2025-07-31', time: '2:00 PM - 4:00 PM', examType: 'MID', courseCode: 'CSE321', courseName: 'Operating Systems' },
      { id: '3', date: '2025-08-02', time: '11:00 AM - 1:00 PM', examType: 'MID', courseCode: 'CSE340', courseName: 'Computer Architecture' },
      { id: '4', date: '2025-08-03', time: '11:00 AM - 1:00 PM', examType: 'MID', courseCode: 'CST204', courseName: 'Communication for Social Change' },
      { id: '5', date: '2025-08-14', time: '4:30 PM - 6:30 PM', examType: 'FINAL', courseCode: 'CSE460', courseName: 'VLSI Design' },
      { id: '6', date: '2025-08-19', time: '2:00 PM - 4:00 PM', examType: 'FINAL', courseCode: 'CSE321', courseName: 'Operating Systems' },
      { id: '7', date: '2025-08-20', time: '11:00 AM - 1:00 PM', examType: 'FINAL', courseCode: 'CSE340', courseName: 'Computer Architecture' },
      { id: '8', date: '2025-08-21', time: '11:00 AM - 1:00 PM', examType: 'FINAL', courseCode: 'CST204', courseName: 'Communication for Social Change' },
    ];

    const sampleEmails: Email[] = [
      {
        id: '1',
        subject: 'Assignment Submission Reminder',
        sender: '<EMAIL>',
        date: new Date(),
        isRead: false
      },
      {
        id: '2',
        subject: 'Course Material Updated',
        sender: '<EMAIL>',
        date: new Date(),
        isRead: false
      }
    ];

    const sampleSyllabus: SyllabusItem[] = [
      {
        id: '1',
        subject: 'CSE340',
        topic: 'CPU Design & Instruction Set Architecture',
        week: 8,
        status: 'current'
      },
      {
        id: '2',
        subject: 'CSE321',
        topic: 'Process Scheduling & Memory Management',
        week: 8,
        status: 'current'
      },
      {
        id: '3',
        subject: 'CSE460',
        topic: 'Digital Circuit Design & Verilog HDL',
        week: 8,
        status: 'current'
      },
      {
        id: '4',
        subject: 'CSE400',
        topic: 'Project Planning & Literature Review',
        week: 8,
        status: 'current'
      },
      {
        id: '5',
        subject: 'CST204',
        topic: 'Communication Strategies for Social Impact',
        week: 9,
        status: 'upcoming'
      }
    ];

    setAssignments(sampleAssignments);
    setNotices(sampleNotices);
    setSchedule(sampleSchedule);
    setExams(sampleExams);
    setEmails(sampleEmails);
    setSyllabus(sampleSyllabus);
  }, []);

  const getTimeUntilDeadline = (dueDate: Date) => {
    const now = new Date();
    if (isBefore(dueDate, now)) {
      return 'OVERDUE';
    }
    return formatDistanceToNow(dueDate, { addSuffix: true });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'classroom': return <GraduationCap className="w-4 h-4" />;
      case 'slack': return <Users className="w-4 h-4" />;
      case 'discord': return <Users className="w-4 h-4" />;
      case 'email': return <Mail className="w-4 h-4" />;
      default: return <Bell className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4" style={{ width: '1024px', height: '600px' }}>
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg p-4 mb-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Academic Dashboard</h1>
            <p className="text-gray-600">CSE Student Portal</p>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-gray-800">
              {currentTime.toLocaleTimeString()}
            </div>
            <div className="text-sm text-gray-600">
              {currentTime.toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-3 gap-4 h-[calc(100%-120px)]">
        {/* Left Column - Assignments & Deadlines */}
        <div className="space-y-4">
          {/* Assignments with Countdown */}
          <div className="bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto">
            <div className="flex items-center mb-3">
              <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
              <h2 className="text-lg font-semibold text-gray-800">Upcoming Deadlines</h2>
            </div>
            <div className="space-y-3">
              {assignments.map((assignment) => (
                <div key={assignment.id} className={`p-3 rounded-lg border ${getPriorityColor(assignment.priority)}`}>
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center">
                      {getSourceIcon(assignment.source)}
                      <span className="ml-2 font-medium text-sm">{assignment.subject}</span>
                    </div>
                    <span className="text-xs font-bold">
                      {getTimeUntilDeadline(assignment.dueDate)}
                    </span>
                  </div>
                  <h3 className="font-semibold text-sm mb-1">{assignment.title}</h3>
                  <p className="text-xs text-gray-600">{assignment.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Today's Schedule */}
          <div className="bg-white rounded-lg shadow-lg p-4 h-48 overflow-y-auto">
            <div className="flex items-center mb-3">
              <Calendar className="w-5 h-5 text-blue-500 mr-2" />
              <h2 className="text-lg font-semibold text-gray-800">Today's Classes</h2>
            </div>
            <div className="space-y-2">
              {schedule
                .filter(class_item => class_item.day === currentTime.toLocaleDateString('en-US', { weekday: 'long' }))
                .map((class_item) => (
                <div key={class_item.id} className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-semibold text-sm">{class_item.subject}</h3>
                      <p className="text-xs text-blue-600 font-medium">
                        {class_item.courseCode} - Section {class_item.section}
                      </p>
                      <p className="text-xs text-gray-600">Faculty: {class_item.faculty}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold text-blue-600">{class_item.time}</div>
                      <div className="text-xs text-gray-600">Room: {class_item.room}</div>
                      <div className="text-xs text-green-600 font-medium">{class_item.duration}</div>
                    </div>
                  </div>
                </div>
              ))}
              {schedule.filter(class_item => class_item.day === currentTime.toLocaleDateString('en-US', { weekday: 'long' })).length === 0 && (
                <div className="text-center text-gray-500 text-sm py-4">
                  No classes scheduled for today
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Middle Column - Notices & Syllabus */}
        <div className="space-y-4">
          {/* Notices */}
          <div className="bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto">
            <div className="flex items-center mb-3">
              <Bell className="w-5 h-5 text-orange-500 mr-2" />
              <h2 className="text-lg font-semibold text-gray-800">Recent Notices</h2>
            </div>
            <div className="space-y-3">
              {notices.map((notice) => (
                <div key={notice.id} className={`p-3 rounded-lg border ${getPriorityColor(notice.priority)}`}>
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center">
                      {getSourceIcon(notice.source)}
                      <span className="ml-2 text-xs text-gray-500">
                        {formatDistanceToNow(notice.date, { addSuffix: true })}
                      </span>
                    </div>
                  </div>
                  <h3 className="font-semibold text-sm mb-1">{notice.title}</h3>
                  <p className="text-xs text-gray-600">{notice.content}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Current Syllabus */}
          <div className="bg-white rounded-lg shadow-lg p-4 h-48 overflow-y-auto">
            <div className="flex items-center mb-3">
              <BookOpen className="w-5 h-5 text-green-500 mr-2" />
              <h2 className="text-lg font-semibold text-gray-800">Current Syllabus</h2>
            </div>
            <div className="space-y-2">
              {syllabus.map((item) => (
                <div key={item.id} className="p-2 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="font-semibold text-sm">{item.subject}</h3>
                      <p className="text-xs text-gray-600">{item.topic}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-green-600 font-semibold">Week {item.week}</div>
                      <div className={`text-xs px-2 py-1 rounded ${
                        item.status === 'current' ? 'bg-green-200 text-green-800' :
                        item.status === 'completed' ? 'bg-gray-200 text-gray-800' :
                        'bg-blue-200 text-blue-800'
                      }`}>
                        {item.status}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Exams & Quick Stats */}
        <div className="space-y-4">
          {/* Upcoming Exams */}
          <div className="bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto">
            <div className="flex items-center mb-3">
              <GraduationCap className="w-5 h-5 text-red-500 mr-2" />
              <h2 className="text-lg font-semibold text-gray-800">Upcoming Exams</h2>
            </div>
            <div className="space-y-2">
              {exams
                .filter(exam => isAfter(new Date(exam.date), new Date()))
                .slice(0, 6)
                .map((exam) => (
                <div key={exam.id} className={`p-2 rounded-lg border ${
                  exam.examType === 'FINAL' ? 'bg-red-50 border-red-200' : 'bg-orange-50 border-orange-200'
                }`}>
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="font-semibold text-sm">{exam.courseName}</h3>
                      <p className="text-xs font-medium text-gray-700">{exam.courseCode}</p>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded font-bold ${
                      exam.examType === 'FINAL' ? 'bg-red-200 text-red-800' : 'bg-orange-200 text-orange-800'
                    }`}>
                      {exam.examType}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600">{exam.date}</p>
                  <p className="text-xs text-gray-500">{exam.time}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-lg shadow-lg p-4 h-48">
            <div className="flex items-center mb-3">
              <CheckCircle className="w-5 h-5 text-indigo-500 mr-2" />
              <h2 className="text-lg font-semibold text-gray-800">Quick Stats</h2>
            </div>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-red-50 p-3 rounded-lg border border-red-200">
                <div className="text-2xl font-bold text-red-600">{assignments.length}</div>
                <div className="text-xs text-red-600">Pending Tasks</div>
              </div>
              <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                <div className="text-2xl font-bold text-blue-600">
                  {schedule.filter(c => c.day === currentTime.toLocaleDateString('en-US', { weekday: 'long' })).length}
                </div>
                <div className="text-xs text-blue-600">Today's Classes</div>
              </div>
              <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                <div className="text-2xl font-bold text-orange-600">
                  {exams.filter(e => isAfter(new Date(e.date), new Date())).length}
                </div>
                <div className="text-xs text-orange-600">Upcoming Exams</div>
              </div>
              <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                <div className="text-2xl font-bold text-green-600">
                  {syllabus.filter(s => s.status === 'current').length}
                </div>
                <div className="text-xs text-green-600">Current Topics</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AcademicDashboard;
