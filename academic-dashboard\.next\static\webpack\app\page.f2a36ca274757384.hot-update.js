"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AcademicDashboard.tsx":
/*!**********************************************!*\
  !*** ./src/components/AcademicDashboard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isBefore.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isAfter.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst AcademicDashboard = ()=>{\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notices, setNotices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedule, setSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exams, setExams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [emails, setEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [syllabus, setSyllabus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Update time every second for real-time countdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"AcademicDashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"AcademicDashboard.useEffect.timer\"], 1000);\n            return ({\n                \"AcademicDashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"AcademicDashboard.useEffect\"];\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    // Mock data - In real implementation, this would come from APIs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            // Sample assignments based on your actual courses with auto-detected deadlines\n            const sampleAssignments = [\n                {\n                    id: '1',\n                    title: 'Assignment 4 (Ch 5) - Memory Hierarchy',\n                    subject: 'CSE340',\n                    dueDate: new Date('2025-09-03'),\n                    description: 'Cache levels, policies, direct mapped cache simulations',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '2',\n                    title: 'HW 07 - Ch 5 (Memory & Caches)',\n                    subject: 'CSE340',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 2),\n                    description: 'Memory hierarchy & locality principles, cache hit/miss calculations',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '3',\n                    title: 'File Systems Implementation Lab',\n                    subject: 'CSE321',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 5),\n                    description: 'FSCK and Journaling implementation - Chapter 40 & 42 (OSTEP)',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'slack'\n                },\n                {\n                    id: '4',\n                    title: 'VLSI Design Verilog Project',\n                    subject: 'CSE460',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 7),\n                    description: 'Digital circuit design using Verilog HDL - Lab Assignment',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'discord'\n                },\n                {\n                    id: '5',\n                    title: 'Graded Assignment 4: Community Devcom Campaign',\n                    subject: 'CST204',\n                    dueDate: new Date('2025-08-25'),\n                    description: 'Community Development Communication Campaign project',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '6',\n                    title: 'Take Home Quiz 3: Inverted Page Table',\n                    subject: 'CSE321',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 9),\n                    description: 'Memory management concepts - Inverted Page Table implementation',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '7',\n                    title: 'Quiz 4 (Ch 4) - Datapath & Pipelining',\n                    subject: 'CSE340',\n                    dueDate: new Date('2025-09-08'),\n                    description: 'Pipelined datapaths, hazards, and hazard resolution',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '8',\n                    title: 'CST204: Final Project Presentation',\n                    subject: 'CST204',\n                    dueDate: new Date('2025-09-04'),\n                    description: 'Final presentation on development communication project',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '9',\n                    title: 'CST204: Policy Brief Submission',\n                    subject: 'CST204',\n                    dueDate: new Date('2025-08-30'),\n                    description: 'Policy analysis and brief on communication for development',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                }\n            ];\n            const sampleNotices = [\n                {\n                    id: '1',\n                    title: 'Mid-term Exam Schedule Released',\n                    content: 'Check your email for detailed exam timetable. First exam: VLSI Design on July 26',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '2',\n                    title: 'CSE340: Memory Hierarchy & Caches (Week 9)',\n                    content: 'Current topic: Cache levels, policies, direct mapped simulations. HW 07 due soon.',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '3',\n                    title: 'CST204: Community Devcom Campaign Due Aug 25',\n                    content: 'Graded Assignment 4 - Community Development Communication Campaign project',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '4',\n                    title: 'CSE340: Quiz 4 Coming Up (Sept 8)',\n                    content: 'Quiz 4 on Chapter 4 - Datapath & Pipelining. Review hazards and hazard resolution.',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                }\n            ];\n            // Your exact class schedule as provided\n            const sampleSchedule = [\n                // Sunday\n                {\n                    id: '1',\n                    subject: 'Computer Architecture',\n                    courseCode: 'CSE340',\n                    section: '08',\n                    faculty: 'PBK',\n                    time: '8:00 AM - 9:20 AM',\n                    room: '09A-06C',\n                    day: 'Sunday',\n                    duration: '1 hr 20 mins'\n                },\n                {\n                    id: '2',\n                    subject: 'Operating Systems',\n                    courseCode: 'CSE321',\n                    section: '14',\n                    faculty: 'ZMD',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '09D-18C',\n                    day: 'Sunday',\n                    duration: '1 hr 20 mins'\n                },\n                // Tuesday\n                {\n                    id: '3',\n                    subject: 'Computer Architecture',\n                    courseCode: 'CSE340',\n                    section: '08',\n                    faculty: 'PBK',\n                    time: '8:00 AM - 9:20 AM',\n                    room: '09A-06C',\n                    day: 'Tuesday',\n                    duration: '1 hr 20 mins'\n                },\n                {\n                    id: '4',\n                    subject: 'Operating Systems',\n                    courseCode: 'CSE321',\n                    section: '14',\n                    faculty: 'ZMD',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '09D-18C',\n                    day: 'Tuesday',\n                    duration: '1 hr 20 mins'\n                },\n                // Wednesday\n                {\n                    id: '5',\n                    subject: 'VLSI Design Lab',\n                    courseCode: 'CSE460L',\n                    section: '06',\n                    faculty: 'TBA',\n                    time: '8:00 AM - 10:50 AM',\n                    room: '09F-24L',\n                    day: 'Wednesday',\n                    duration: '2 hrs 50 mins'\n                },\n                {\n                    id: '6',\n                    subject: 'Operating Systems Lab',\n                    courseCode: 'CSE321L',\n                    section: '14',\n                    faculty: 'TBA',\n                    time: '11:00 AM - 1:50 PM',\n                    room: '09F-25L',\n                    day: 'Wednesday',\n                    duration: '2 hrs 50 mins'\n                },\n                // Thursday\n                {\n                    id: '7',\n                    subject: 'VLSI Design Theory',\n                    courseCode: 'CSE460',\n                    section: '06',\n                    faculty: 'YAP',\n                    time: '8:00 AM - 10:50 AM',\n                    room: '09D-17C',\n                    day: 'Thursday',\n                    duration: '2 hrs 50 mins'\n                },\n                {\n                    id: '8',\n                    subject: 'Communication for Social Change',\n                    courseCode: 'CST204',\n                    section: '01',\n                    faculty: 'MFC',\n                    time: '11:00 AM - 1:50 PM',\n                    room: '09E-23C',\n                    day: 'Thursday',\n                    duration: '2 hrs 50 mins'\n                },\n                // Saturday\n                {\n                    id: '9',\n                    subject: 'VLSI Design Theory',\n                    courseCode: 'CSE460',\n                    section: '06',\n                    faculty: 'YAP',\n                    time: '8:00 AM - 10:50 AM',\n                    room: '09D-17C',\n                    day: 'Saturday',\n                    duration: '2 hrs 50 mins'\n                },\n                {\n                    id: '10',\n                    subject: 'Communication for Social Change',\n                    courseCode: 'CST204',\n                    section: '01',\n                    faculty: 'MFC',\n                    time: '11:00 AM - 1:50 PM',\n                    room: '09E-23C',\n                    day: 'Saturday',\n                    duration: '2 hrs 50 mins'\n                }\n            ];\n            // Your exact exam schedule as provided\n            const sampleExams = [\n                // MID TERM EXAMS\n                {\n                    id: '1',\n                    date: '2025-07-26',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE460',\n                    courseName: 'VLSI Design'\n                },\n                {\n                    id: '2',\n                    date: '2025-07-31',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE321',\n                    courseName: 'Operating Systems'\n                },\n                {\n                    id: '3',\n                    date: '2025-08-02',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE340',\n                    courseName: 'Computer Architecture'\n                },\n                {\n                    id: '4',\n                    date: '2025-08-03',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CST204',\n                    courseName: 'Communication for Social Change'\n                },\n                // FINAL EXAMS\n                {\n                    id: '5',\n                    date: '2025-09-14',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE460',\n                    courseName: 'VLSI Design'\n                },\n                {\n                    id: '6',\n                    date: '2025-09-19',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE321',\n                    courseName: 'Operating Systems'\n                },\n                {\n                    id: '7',\n                    date: '2025-09-20',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE340',\n                    courseName: 'Computer Architecture'\n                },\n                {\n                    id: '8',\n                    date: '2025-09-21',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CST204',\n                    courseName: 'Communication for Social Change'\n                }\n            ];\n            const sampleEmails = [\n                {\n                    id: '1',\n                    subject: 'Assignment Submission Reminder',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                },\n                {\n                    id: '2',\n                    subject: 'Course Material Updated',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                }\n            ];\n            const sampleSyllabus = [\n                {\n                    id: '1',\n                    subject: 'CSE340',\n                    topic: 'Computer Architecture - Current Topics',\n                    week: 9,\n                    status: 'current'\n                },\n                {\n                    id: '2',\n                    subject: 'CSE321',\n                    topic: 'Operating Systems - Current Topics',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '3',\n                    subject: 'CSE460',\n                    topic: 'VLSI Design - Current Topics',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '4',\n                    subject: 'CST204',\n                    topic: 'Communication for Social Change - Current Topics',\n                    week: 8,\n                    status: 'current'\n                }\n            ];\n            setAssignments(sampleAssignments);\n            setNotices(sampleNotices);\n            setSchedule(sampleSchedule);\n            setExams(sampleExams);\n            setEmails(sampleEmails);\n            setSyllabus(sampleSyllabus);\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    const getTimeUntilDeadline = (dueDate)=>{\n        const now = new Date();\n        if ((0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__.isBefore)(dueDate, now)) {\n            return 'OVERDUE';\n        }\n        return (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(dueDate, {\n            addSuffix: true\n        });\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50 border-red-200';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n            case 'low':\n                return 'text-green-600 bg-green-50 border-green-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    const getSourceIcon = (source)=>{\n        switch(source){\n            case 'classroom':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 32\n                }, undefined);\n            case 'slack':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 28\n                }, undefined);\n            case 'discord':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 30\n                }, undefined);\n            case 'email':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 28\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 431,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-screen h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-2 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-3 mb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Academic Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 font-medium\",\n                                    children: \"CSE Student Portal\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold text-gray-900\",\n                                    children: currentTime.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700 font-medium\",\n                                    children: currentTime.toLocaleDateString('en-US', {\n                                        weekday: 'long',\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-2 h-[calc(100vh-100px)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Deadlines\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: assignments.map((assignment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(assignment.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    getSourceIcon(assignment.source),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 font-bold text-base text-gray-900\",\n                                                                        children: assignment.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold text-red-700\",\n                                                                children: getTimeUntilDeadline(assignment.dueDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-base mb-1 text-gray-900\",\n                                                        children: assignment.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: assignment.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, assignment.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Today's Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            schedule.filter((class_item)=>class_item.day === currentTime.toLocaleDateString('en-US', {\n                                                    weekday: 'long'\n                                                })).map((class_item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-50 rounded-lg border border-blue-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-base text-gray-900\",\n                                                                        children: class_item.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-blue-800 font-bold\",\n                                                                        children: [\n                                                                            class_item.courseCode,\n                                                                            \" - Section \",\n                                                                            class_item.section\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-800 font-medium\",\n                                                                        children: [\n                                                                            \"Faculty: \",\n                                                                            class_item.faculty\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-blue-800\",\n                                                                        children: class_item.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-800 font-medium\",\n                                                                        children: [\n                                                                            \"Room: \",\n                                                                            class_item.room\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-green-700 font-bold\",\n                                                                        children: class_item.duration\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, class_item.id, false, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, undefined)),\n                                            schedule.filter((class_item)=>class_item.day === currentTime.toLocaleDateString('en-US', {\n                                                    weekday: 'long'\n                                                })).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500 text-sm py-4\",\n                                                children: \"No classes scheduled for today\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Recent Notices\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: notices.map((notice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(notice.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getSourceIcon(notice.source),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-xs text-gray-500\",\n                                                                    children: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(notice.date, {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-sm mb-1\",\n                                                        children: notice.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: notice.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, notice.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Current Syllabus\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: syllabus.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-50 rounded-lg border border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: item.subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: item.topic\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 font-semibold\",\n                                                                    children: [\n                                                                        \"Week \",\n                                                                        item.week\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs px-2 py-1 rounded \".concat(item.status === 'current' ? 'bg-green-200 text-green-800' : item.status === 'completed' ? 'bg-gray-200 text-gray-800' : 'bg-blue-200 text-blue-800'),\n                                                                    children: item.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, item.id, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Exams\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: exams.filter((exam)=>(0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_12__.isAfter)(new Date(exam.date), new Date())).slice(0, 6).map((exam)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg border \".concat(exam.examType === 'FINAL' ? 'bg-red-50 border-red-200' : 'bg-orange-50 border-orange-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-sm\",\n                                                                        children: exam.courseName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-gray-700\",\n                                                                        children: exam.courseCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded font-bold \".concat(exam.examType === 'FINAL' ? 'bg-red-200 text-red-800' : 'bg-orange-200 text-orange-800'),\n                                                                children: exam.examType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: exam.date\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: exam.time\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, exam.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 text-indigo-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-3 rounded-lg border border-red-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: assignments.length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-red-600\",\n                                                        children: \"Pending Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: schedule.filter((c)=>c.day === currentTime.toLocaleDateString('en-US', {\n                                                                weekday: 'long'\n                                                            })).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-600\",\n                                                        children: \"Today's Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 p-3 rounded-lg border border-orange-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-600\",\n                                                        children: exams.filter((e)=>(0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_12__.isAfter)(new Date(e.date), new Date())).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-orange-600\",\n                                                        children: \"Upcoming Exams\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-3 rounded-lg border border-green-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: syllabus.filter((s)=>s.status === 'current').length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-600\",\n                                                        children: \"Current Topics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 583,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 461,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AcademicDashboard, \"QqYYjitf6Dwya1H9wXNNpC4jF60=\");\n_c = AcademicDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AcademicDashboard);\nvar _c;\n$RefreshReg$(_c, \"AcademicDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AcademicDashboard.tsx\n"));

/***/ })

});