"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AcademicDashboard.tsx":
/*!**********************************************!*\
  !*** ./src/components/AcademicDashboard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isBefore.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isAfter.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst AcademicDashboard = ()=>{\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notices, setNotices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedule, setSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exams, setExams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [emails, setEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [syllabus, setSyllabus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Update time every second for real-time countdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"AcademicDashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"AcademicDashboard.useEffect.timer\"], 1000);\n            return ({\n                \"AcademicDashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"AcademicDashboard.useEffect\"];\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    // Mock data - In real implementation, this would come from APIs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            // Sample assignments based on your actual courses with auto-detected deadlines\n            const sampleAssignments = [\n                {\n                    id: '1',\n                    title: 'Computer Architecture Assignment 3',\n                    subject: 'CSE340',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 3),\n                    description: 'CPU design and instruction set architecture analysis',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '2',\n                    title: 'Operating Systems Lab Report',\n                    subject: 'CSE321',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 5),\n                    description: 'Process scheduling and memory management implementation',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'slack'\n                },\n                {\n                    id: '3',\n                    title: 'VLSI Design Verilog Project',\n                    subject: 'CSE460',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 7),\n                    description: 'Digital circuit design using Verilog HDL - Lab Assignment',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'discord'\n                },\n                {\n                    id: '4',\n                    title: 'Communication Research Paper',\n                    subject: 'CST204',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 4),\n                    description: 'Social change communication strategies analysis',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '5',\n                    title: 'Operating Systems Theory Assignment',\n                    subject: 'CSE321',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 6),\n                    description: 'Memory management and file system concepts',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                }\n            ];\n            const sampleNotices = [\n                {\n                    id: '1',\n                    title: 'Mid-term Exam Schedule Released',\n                    content: 'Check your email for detailed exam timetable. First exam: VLSI Design on July 26',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '2',\n                    title: 'Operating Systems Lab Rescheduled',\n                    content: 'CSE321L lab session confirmed for Wednesday 11:00 AM - 1:50 PM in room 09F-25L',\n                    date: new Date(),\n                    source: 'slack',\n                    priority: 'medium'\n                },\n                {\n                    id: '3',\n                    title: 'VLSI Design Lab Equipment Update',\n                    content: 'New Verilog simulation tools available in lab 09F-24L. Training session tomorrow.',\n                    date: new Date(),\n                    source: 'discord',\n                    priority: 'medium'\n                },\n                {\n                    id: '4',\n                    title: 'Communication Course Assignment',\n                    content: 'CST204 research paper guidelines posted. Due date extended by 2 days.',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'medium'\n                }\n            ];\n            // Your exact class schedule as provided\n            const sampleSchedule = [\n                // Sunday\n                {\n                    id: '1',\n                    subject: 'Computer Architecture',\n                    courseCode: 'CSE340',\n                    section: '08',\n                    faculty: 'PBK',\n                    time: '8:00 AM - 9:20 AM',\n                    room: '09A-06C',\n                    day: 'Sunday',\n                    duration: '1 hr 20 mins'\n                },\n                {\n                    id: '2',\n                    subject: 'Operating Systems',\n                    courseCode: 'CSE321',\n                    section: '14',\n                    faculty: 'ZMD',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '09D-18C',\n                    day: 'Sunday',\n                    duration: '1 hr 20 mins'\n                },\n                // Tuesday\n                {\n                    id: '3',\n                    subject: 'Computer Architecture',\n                    courseCode: 'CSE340',\n                    section: '08',\n                    faculty: 'PBK',\n                    time: '8:00 AM - 9:20 AM',\n                    room: '09A-06C',\n                    day: 'Tuesday',\n                    duration: '1 hr 20 mins'\n                },\n                {\n                    id: '4',\n                    subject: 'Operating Systems',\n                    courseCode: 'CSE321',\n                    section: '14',\n                    faculty: 'ZMD',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '09D-18C',\n                    day: 'Tuesday',\n                    duration: '1 hr 20 mins'\n                },\n                // Wednesday\n                {\n                    id: '5',\n                    subject: 'VLSI Design Lab',\n                    courseCode: 'CSE460L',\n                    section: '06',\n                    faculty: 'TBA',\n                    time: '8:00 AM - 10:50 AM',\n                    room: '09F-24L',\n                    day: 'Wednesday',\n                    duration: '2 hrs 50 mins'\n                },\n                {\n                    id: '6',\n                    subject: 'Operating Systems Lab',\n                    courseCode: 'CSE321L',\n                    section: '14',\n                    faculty: 'TBA',\n                    time: '11:00 AM - 1:50 PM',\n                    room: '09F-25L',\n                    day: 'Wednesday',\n                    duration: '2 hrs 50 mins'\n                },\n                // Thursday\n                {\n                    id: '7',\n                    subject: 'VLSI Design Theory',\n                    courseCode: 'CSE460',\n                    section: '06',\n                    faculty: 'YAP',\n                    time: '8:00 AM - 10:50 AM',\n                    room: '09D-17C',\n                    day: 'Thursday',\n                    duration: '2 hrs 50 mins'\n                },\n                {\n                    id: '8',\n                    subject: 'Communication for Social Change',\n                    courseCode: 'CST204',\n                    section: '01',\n                    faculty: 'MFC',\n                    time: '11:00 AM - 1:50 PM',\n                    room: '09E-23C',\n                    day: 'Thursday',\n                    duration: '2 hrs 50 mins'\n                },\n                // Saturday\n                {\n                    id: '9',\n                    subject: 'VLSI Design Theory',\n                    courseCode: 'CSE460',\n                    section: '06',\n                    faculty: 'YAP',\n                    time: '8:00 AM - 10:50 AM',\n                    room: '09D-17C',\n                    day: 'Saturday',\n                    duration: '2 hrs 50 mins'\n                },\n                {\n                    id: '10',\n                    subject: 'Communication for Social Change',\n                    courseCode: 'CST204',\n                    section: '01',\n                    faculty: 'MFC',\n                    time: '11:00 AM - 1:50 PM',\n                    room: '09E-23C',\n                    day: 'Saturday',\n                    duration: '2 hrs 50 mins'\n                }\n            ];\n            // Your exact exam schedule as provided\n            const sampleExams = [\n                // MID TERM EXAMS\n                {\n                    id: '1',\n                    date: '2025-07-26',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE460',\n                    courseName: 'VLSI Design'\n                },\n                {\n                    id: '2',\n                    date: '2025-07-31',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE321',\n                    courseName: 'Operating Systems'\n                },\n                {\n                    id: '3',\n                    date: '2025-08-02',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE340',\n                    courseName: 'Computer Architecture'\n                },\n                {\n                    id: '4',\n                    date: '2025-08-03',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CST204',\n                    courseName: 'Communication for Social Change'\n                },\n                // FINAL EXAMS\n                {\n                    id: '5',\n                    date: '2025-09-14',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE460',\n                    courseName: 'VLSI Design'\n                },\n                {\n                    id: '6',\n                    date: '2025-09-19',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE321',\n                    courseName: 'Operating Systems'\n                },\n                {\n                    id: '7',\n                    date: '2025-09-20',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE340',\n                    courseName: 'Computer Architecture'\n                },\n                {\n                    id: '8',\n                    date: '2025-09-21',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CST204',\n                    courseName: 'Communication for Social Change'\n                }\n            ];\n            const sampleEmails = [\n                {\n                    id: '1',\n                    subject: 'Assignment Submission Reminder',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                },\n                {\n                    id: '2',\n                    subject: 'Course Material Updated',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                }\n            ];\n            const sampleSyllabus = [\n                // CSE340 - Computer Architecture (Current Progress)\n                {\n                    id: '1',\n                    subject: 'CSE340',\n                    topic: 'RISC-V non-pipelined datapath/control path (Week 8)',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '2',\n                    subject: 'CSE340',\n                    topic: 'RISC-V pipelined datapath & Hazards (Week 9)',\n                    week: 9,\n                    status: 'upcoming'\n                },\n                {\n                    id: '3',\n                    subject: 'CSE340',\n                    topic: 'Memory Hierarchy & Cache Performance (Week 10-11)',\n                    week: 10,\n                    status: 'upcoming'\n                },\n                // Other Courses\n                {\n                    id: '4',\n                    subject: 'CSE321',\n                    topic: 'Process Scheduling & Memory Management',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '5',\n                    subject: 'CSE460',\n                    topic: 'Digital Circuit Design & Verilog HDL',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '6',\n                    subject: 'CST204',\n                    topic: 'Communication Strategies for Social Impact',\n                    week: 8,\n                    status: 'current'\n                }\n            ];\n            setAssignments(sampleAssignments);\n            setNotices(sampleNotices);\n            setSchedule(sampleSchedule);\n            setExams(sampleExams);\n            setEmails(sampleEmails);\n            setSyllabus(sampleSyllabus);\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    const getTimeUntilDeadline = (dueDate)=>{\n        const now = new Date();\n        if ((0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__.isBefore)(dueDate, now)) {\n            return 'OVERDUE';\n        }\n        return (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(dueDate, {\n            addSuffix: true\n        });\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50 border-red-200';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n            case 'low':\n                return 'text-green-600 bg-green-50 border-green-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    const getSourceIcon = (source)=>{\n        switch(source){\n            case 'classroom':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 32\n                }, undefined);\n            case 'slack':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 28\n                }, undefined);\n            case 'discord':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 30\n                }, undefined);\n            case 'email':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 28\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-screen h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-2 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-3 mb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Academic Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 font-medium\",\n                                    children: \"CSE Student Portal\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold text-gray-900\",\n                                    children: currentTime.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700 font-medium\",\n                                    children: currentTime.toLocaleDateString('en-US', {\n                                        weekday: 'long',\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-2 h-[calc(100vh-100px)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Deadlines\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: assignments.map((assignment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(assignment.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    getSourceIcon(assignment.source),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 font-bold text-base text-gray-900\",\n                                                                        children: assignment.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold text-red-700\",\n                                                                children: getTimeUntilDeadline(assignment.dueDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-base mb-1 text-gray-900\",\n                                                        children: assignment.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: assignment.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, assignment.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Today's Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            schedule.filter((class_item)=>class_item.day === currentTime.toLocaleDateString('en-US', {\n                                                    weekday: 'long'\n                                                })).map((class_item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-50 rounded-lg border border-blue-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-base text-gray-900\",\n                                                                        children: class_item.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-blue-800 font-bold\",\n                                                                        children: [\n                                                                            class_item.courseCode,\n                                                                            \" - Section \",\n                                                                            class_item.section\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-800 font-medium\",\n                                                                        children: [\n                                                                            \"Faculty: \",\n                                                                            class_item.faculty\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-blue-800\",\n                                                                        children: class_item.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-800 font-medium\",\n                                                                        children: [\n                                                                            \"Room: \",\n                                                                            class_item.room\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-green-700 font-bold\",\n                                                                        children: class_item.duration\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, class_item.id, false, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, undefined)),\n                                            schedule.filter((class_item)=>class_item.day === currentTime.toLocaleDateString('en-US', {\n                                                    weekday: 'long'\n                                                })).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500 text-sm py-4\",\n                                                children: \"No classes scheduled for today\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Recent Notices\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: notices.map((notice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(notice.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getSourceIcon(notice.source),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-xs text-gray-500\",\n                                                                    children: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(notice.date, {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-sm mb-1\",\n                                                        children: notice.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: notice.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, notice.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Current Syllabus\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: syllabus.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-50 rounded-lg border border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: item.subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: item.topic\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 font-semibold\",\n                                                                    children: [\n                                                                        \"Week \",\n                                                                        item.week\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs px-2 py-1 rounded \".concat(item.status === 'current' ? 'bg-green-200 text-green-800' : item.status === 'completed' ? 'bg-gray-200 text-gray-800' : 'bg-blue-200 text-blue-800'),\n                                                                    children: item.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, item.id, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Exams\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: exams.filter((exam)=>(0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_12__.isAfter)(new Date(exam.date), new Date())).slice(0, 6).map((exam)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg border \".concat(exam.examType === 'FINAL' ? 'bg-red-50 border-red-200' : 'bg-orange-50 border-orange-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-sm\",\n                                                                        children: exam.courseName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-gray-700\",\n                                                                        children: exam.courseCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded font-bold \".concat(exam.examType === 'FINAL' ? 'bg-red-200 text-red-800' : 'bg-orange-200 text-orange-800'),\n                                                                children: exam.examType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: exam.date\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: exam.time\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, exam.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 text-indigo-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-3 rounded-lg border border-red-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: assignments.length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-red-600\",\n                                                        children: \"Pending Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: schedule.filter((c)=>c.day === currentTime.toLocaleDateString('en-US', {\n                                                                weekday: 'long'\n                                                            })).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-600\",\n                                                        children: \"Today's Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 p-3 rounded-lg border border-orange-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-600\",\n                                                        children: exams.filter((e)=>(0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_12__.isAfter)(new Date(e.date), new Date())).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-orange-600\",\n                                                        children: \"Upcoming Exams\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-3 rounded-lg border border-green-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: syllabus.filter((s)=>s.status === 'current').length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-600\",\n                                                        children: \"Current Topics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n        lineNumber: 413,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AcademicDashboard, \"QqYYjitf6Dwya1H9wXNNpC4jF60=\");\n_c = AcademicDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AcademicDashboard);\nvar _c;\n$RefreshReg$(_c, \"AcademicDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AcademicDashboard.tsx\n"));

/***/ })

});