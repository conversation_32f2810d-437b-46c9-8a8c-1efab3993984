"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AcademicDashboard.tsx":
/*!**********************************************!*\
  !*** ./src/components/AcademicDashboard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,GraduationCap,Mail,MessageSquare,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,GraduationCap,Mail,MessageSquare,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,GraduationCap,Mail,MessageSquare,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,GraduationCap,Mail,MessageSquare,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,GraduationCap,Mail,MessageSquare,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,GraduationCap,Mail,MessageSquare,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,GraduationCap,Mail,MessageSquare,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,GraduationCap,Mail,MessageSquare,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,GraduationCap,Mail,MessageSquare,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isBefore.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isAfter.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst AcademicDashboard = ()=>{\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notices, setNotices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedule, setSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exams, setExams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [emails, setEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [syllabus, setSyllabus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Slack Channels\n    const [slackChannels, setSlackChannels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            name: 'cse-general',\n            isConnected: true,\n            lastMessage: 'Assignment deadline reminder for CSE340',\n            lastMessageTime: new Date(),\n            unreadCount: 3\n        },\n        {\n            id: '2',\n            name: 'cse-announcements',\n            isConnected: true,\n            lastMessage: 'Mid-term exam schedule updated',\n            lastMessageTime: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), -1),\n            unreadCount: 1\n        }\n    ]);\n    // Update time every second for real-time countdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"AcademicDashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"AcademicDashboard.useEffect.timer\"], 1000);\n            return ({\n                \"AcademicDashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"AcademicDashboard.useEffect\"];\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    // Mock data - In real implementation, this would come from APIs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            // Sample assignments based on your actual courses with auto-detected deadlines\n            const sampleAssignments = [\n                {\n                    id: '1',\n                    title: 'Assignment 4 (Ch 5) - Memory Hierarchy',\n                    subject: 'CSE340',\n                    dueDate: new Date('2025-09-03'),\n                    description: 'Cache levels, policies, direct mapped cache simulations',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '2',\n                    title: 'HW 07 - Ch 5 (Memory & Caches)',\n                    subject: 'CSE340',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 2),\n                    description: 'Memory hierarchy & locality principles, cache hit/miss calculations',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '3',\n                    title: 'File Systems Implementation Lab',\n                    subject: 'CSE321',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 5),\n                    description: 'FSCK and Journaling implementation - Chapter 40 & 42 (OSTEP)',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'slack'\n                },\n                {\n                    id: '4',\n                    title: 'VLSI Design Verilog Project',\n                    subject: 'CSE460',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 7),\n                    description: 'Digital circuit design using Verilog HDL - Lab Assignment',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'discord'\n                },\n                {\n                    id: '5',\n                    title: 'Graded Assignment 4: Community Devcom Campaign',\n                    subject: 'CST204',\n                    dueDate: new Date('2025-08-25'),\n                    description: 'Community Development Communication Campaign project',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '6',\n                    title: 'Take Home Quiz 3: Inverted Page Table',\n                    subject: 'CSE321',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 9),\n                    description: 'Memory management concepts - Inverted Page Table implementation',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '7',\n                    title: 'Quiz 4 (Ch 4) - Datapath & Pipelining',\n                    subject: 'CSE340',\n                    dueDate: new Date('2025-09-08'),\n                    description: 'Pipelined datapaths, hazards, and hazard resolution',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '8',\n                    title: 'CST204: Final Project Presentation',\n                    subject: 'CST204',\n                    dueDate: new Date('2025-09-04'),\n                    description: 'Final presentation on development communication project',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '9',\n                    title: 'CST204: Policy Brief Submission',\n                    subject: 'CST204',\n                    dueDate: new Date('2025-08-30'),\n                    description: 'Policy analysis and brief on communication for development',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                }\n            ];\n            const sampleNotices = [\n                {\n                    id: '1',\n                    title: 'Mid-term Exam Schedule Released',\n                    content: 'Check your email for detailed exam timetable. First exam: VLSI Design on July 26',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '2',\n                    title: 'CSE340: Memory Hierarchy & Caches (Week 9)',\n                    content: 'Current topic: Cache levels, policies, direct mapped simulations. HW 07 due soon.',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '3',\n                    title: 'CST204: Community Devcom Campaign Due Aug 25',\n                    content: 'Graded Assignment 4 - Community Development Communication Campaign project',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '4',\n                    title: 'CSE340: Quiz 4 Coming Up (Sept 8)',\n                    content: 'Quiz 4 on Chapter 4 - Datapath & Pipelining. Review hazards and hazard resolution.',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                }\n            ];\n            // Your exact class schedule as provided\n            const sampleSchedule = [\n                // Sunday\n                {\n                    id: '1',\n                    subject: 'Computer Architecture',\n                    courseCode: 'CSE340',\n                    section: '08',\n                    faculty: 'PBK',\n                    time: '8:00 AM - 9:20 AM',\n                    room: '09A-06C',\n                    day: 'Sunday',\n                    duration: '1 hr 20 mins'\n                },\n                {\n                    id: '2',\n                    subject: 'Operating Systems',\n                    courseCode: 'CSE321',\n                    section: '14',\n                    faculty: 'ZMD',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '09D-18C',\n                    day: 'Sunday',\n                    duration: '1 hr 20 mins'\n                },\n                // Tuesday\n                {\n                    id: '3',\n                    subject: 'Computer Architecture',\n                    courseCode: 'CSE340',\n                    section: '08',\n                    faculty: 'PBK',\n                    time: '8:00 AM - 9:20 AM',\n                    room: '09A-06C',\n                    day: 'Tuesday',\n                    duration: '1 hr 20 mins'\n                },\n                {\n                    id: '4',\n                    subject: 'Operating Systems',\n                    courseCode: 'CSE321',\n                    section: '14',\n                    faculty: 'ZMD',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '09D-18C',\n                    day: 'Tuesday',\n                    duration: '1 hr 20 mins'\n                },\n                // Wednesday\n                {\n                    id: '5',\n                    subject: 'VLSI Design Lab',\n                    courseCode: 'CSE460L',\n                    section: '06',\n                    faculty: 'TBA',\n                    time: '8:00 AM - 10:50 AM',\n                    room: '09F-24L',\n                    day: 'Wednesday',\n                    duration: '2 hrs 50 mins'\n                },\n                {\n                    id: '6',\n                    subject: 'Operating Systems Lab',\n                    courseCode: 'CSE321L',\n                    section: '14',\n                    faculty: 'TBA',\n                    time: '11:00 AM - 1:50 PM',\n                    room: '09F-25L',\n                    day: 'Wednesday',\n                    duration: '2 hrs 50 mins'\n                },\n                // Thursday\n                {\n                    id: '7',\n                    subject: 'VLSI Design Theory',\n                    courseCode: 'CSE460',\n                    section: '06',\n                    faculty: 'YAP',\n                    time: '8:00 AM - 10:50 AM',\n                    room: '09D-17C',\n                    day: 'Thursday',\n                    duration: '2 hrs 50 mins'\n                },\n                {\n                    id: '8',\n                    subject: 'Communication for Social Change',\n                    courseCode: 'CST204',\n                    section: '01',\n                    faculty: 'MFC',\n                    time: '11:00 AM - 1:50 PM',\n                    room: '09E-23C',\n                    day: 'Thursday',\n                    duration: '2 hrs 50 mins'\n                },\n                // Saturday\n                {\n                    id: '9',\n                    subject: 'VLSI Design Theory',\n                    courseCode: 'CSE460',\n                    section: '06',\n                    faculty: 'YAP',\n                    time: '8:00 AM - 10:50 AM',\n                    room: '09D-17C',\n                    day: 'Saturday',\n                    duration: '2 hrs 50 mins'\n                },\n                {\n                    id: '10',\n                    subject: 'Communication for Social Change',\n                    courseCode: 'CST204',\n                    section: '01',\n                    faculty: 'MFC',\n                    time: '11:00 AM - 1:50 PM',\n                    room: '09E-23C',\n                    day: 'Saturday',\n                    duration: '2 hrs 50 mins'\n                }\n            ];\n            // Your exact exam schedule as provided\n            const sampleExams = [\n                // MID TERM EXAMS\n                {\n                    id: '1',\n                    date: '2025-07-26',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE460',\n                    courseName: 'VLSI Design'\n                },\n                {\n                    id: '2',\n                    date: '2025-07-31',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE321',\n                    courseName: 'Operating Systems'\n                },\n                {\n                    id: '3',\n                    date: '2025-08-02',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE340',\n                    courseName: 'Computer Architecture'\n                },\n                {\n                    id: '4',\n                    date: '2025-08-03',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CST204',\n                    courseName: 'Communication for Social Change'\n                },\n                // FINAL EXAMS\n                {\n                    id: '5',\n                    date: '2025-09-14',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE460',\n                    courseName: 'VLSI Design'\n                },\n                {\n                    id: '6',\n                    date: '2025-09-19',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE321',\n                    courseName: 'Operating Systems'\n                },\n                {\n                    id: '7',\n                    date: '2025-09-20',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE340',\n                    courseName: 'Computer Architecture'\n                },\n                {\n                    id: '8',\n                    date: '2025-09-21',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CST204',\n                    courseName: 'Communication for Social Change'\n                }\n            ];\n            const sampleEmails = [\n                {\n                    id: '1',\n                    subject: 'Assignment Submission Reminder',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                },\n                {\n                    id: '2',\n                    subject: 'Course Material Updated',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                }\n            ];\n            const sampleSyllabus = [\n                {\n                    id: '1',\n                    subject: 'CSE340',\n                    topic: 'Computer Architecture - Current Topics',\n                    week: 9,\n                    status: 'current'\n                },\n                {\n                    id: '2',\n                    subject: 'CSE321',\n                    topic: 'Operating Systems - Current Topics',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '3',\n                    subject: 'CSE460',\n                    topic: 'VLSI Design - Current Topics',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '4',\n                    subject: 'CST204',\n                    topic: 'Communication for Social Change - Current Topics',\n                    week: 8,\n                    status: 'current'\n                }\n            ];\n            setAssignments(sampleAssignments);\n            setNotices(sampleNotices);\n            setSchedule(sampleSchedule);\n            setExams(sampleExams);\n            setEmails(sampleEmails);\n            setSyllabus(sampleSyllabus);\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    const getTimeUntilDeadline = (dueDate)=>{\n        const now = new Date();\n        if ((0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__.isBefore)(dueDate, now)) {\n            return 'OVERDUE';\n        }\n        return (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(dueDate, {\n            addSuffix: true\n        });\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50 border-red-200';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n            case 'low':\n                return 'text-green-600 bg-green-50 border-green-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    const getSourceIcon = (source)=>{\n        switch(source){\n            case 'classroom':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 32\n                }, undefined);\n            case 'slack':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 28\n                }, undefined);\n            case 'discord':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 30\n                }, undefined);\n            case 'email':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 28\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-screen h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-2 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-3 mb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Academic Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 font-medium\",\n                                    children: \"CSE Student Portal\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold text-gray-900\",\n                                    children: currentTime.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700 font-medium\",\n                                    children: currentTime.toLocaleDateString('en-US', {\n                                        weekday: 'long',\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 467,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-2 h-[calc(100vh-100px)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Deadlines\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: assignments.map((assignment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(assignment.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    getSourceIcon(assignment.source),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 font-bold text-base text-gray-900\",\n                                                                        children: assignment.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold text-red-700\",\n                                                                children: getTimeUntilDeadline(assignment.dueDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-base mb-1 text-gray-900\",\n                                                        children: assignment.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: assignment.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, assignment.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Today's Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            schedule.filter((class_item)=>class_item.day === currentTime.toLocaleDateString('en-US', {\n                                                    weekday: 'long'\n                                                })).map((class_item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-50 rounded-lg border border-blue-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-base text-gray-900\",\n                                                                        children: class_item.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-blue-800 font-bold\",\n                                                                        children: [\n                                                                            class_item.courseCode,\n                                                                            \" - Section \",\n                                                                            class_item.section\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-800 font-medium\",\n                                                                        children: [\n                                                                            \"Faculty: \",\n                                                                            class_item.faculty\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-blue-800\",\n                                                                        children: class_item.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 538,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-800 font-medium\",\n                                                                        children: [\n                                                                            \"Room: \",\n                                                                            class_item.room\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-green-700 font-bold\",\n                                                                        children: class_item.duration\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, class_item.id, false, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, undefined)),\n                                            schedule.filter((class_item)=>class_item.day === currentTime.toLocaleDateString('en-US', {\n                                                    weekday: 'long'\n                                                })).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500 text-sm py-4\",\n                                                children: \"No classes scheduled for today\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Recent Notices\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: notices.map((notice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(notice.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getSourceIcon(notice.source),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-xs text-gray-500\",\n                                                                    children: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(notice.date, {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-sm mb-1\",\n                                                        children: notice.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: notice.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, notice.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 text-purple-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Slack Channels\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            slackChannels.map((channel)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-purple-50 rounded-lg border border-purple-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full mr-2 \".concat(channel.isConnected ? 'bg-green-500' : 'bg-red-500')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-bold text-base text-gray-900\",\n                                                                            children: [\n                                                                                \"#\",\n                                                                                channel.name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                channel.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full\",\n                                                                    children: channel.unreadCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        channel.lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-700 mb-1\",\n                                                                    children: channel.lastMessage\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: channel.lastMessageTime && (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(channel.lastMessageTime, {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, channel.id, true, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 17\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full p-3 border-2 border-dashed border-purple-300 rounded-lg text-purple-600 hover:bg-purple-50 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Add Slack Channel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Exams\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: exams.filter((exam)=>(0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_13__.isAfter)(new Date(exam.date), new Date())).slice(0, 6).map((exam)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg border \".concat(exam.examType === 'FINAL' ? 'bg-red-50 border-red-200' : 'bg-orange-50 border-orange-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-sm\",\n                                                                        children: exam.courseName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-gray-700\",\n                                                                        children: exam.courseCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded font-bold \".concat(exam.examType === 'FINAL' ? 'bg-red-200 text-red-800' : 'bg-orange-200 text-orange-800'),\n                                                                children: exam.examType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: exam.date\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: exam.time\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, exam.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-3 h-[45vh]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Calendar_CheckCircle_GraduationCap_Mail_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 text-indigo-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-3 rounded-lg border border-red-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: assignments.length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-red-600\",\n                                                        children: \"Pending Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: schedule.filter((c)=>c.day === currentTime.toLocaleDateString('en-US', {\n                                                                weekday: 'long'\n                                                            })).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-600\",\n                                                        children: \"Today's Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 p-3 rounded-lg border border-orange-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-600\",\n                                                        children: exams.filter((e)=>(0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_13__.isAfter)(new Date(e.date), new Date())).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-orange-600\",\n                                                        children: \"Upcoming Exams\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-50 p-3 rounded-lg border border-purple-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: slackChannels.reduce((total, channel)=>total + channel.unreadCount, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-purple-600\",\n                                                        children: \"Slack Messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n        lineNumber: 465,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AcademicDashboard, \"NdofC3jlcyom2jrRFJha70Po/D0=\");\n_c = AcademicDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AcademicDashboard);\nvar _c;\n$RefreshReg$(_c, \"AcademicDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FjYWRlbWljRGFzaGJvYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ21HO0FBQzNFO0FBcUUzRSxNQUFNZ0Isb0JBQThCOztJQUNsQyxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR2pCLCtDQUFRQSxDQUFDLElBQUlrQjtJQUNuRCxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR3BCLCtDQUFRQSxDQUFlLEVBQUU7SUFDL0QsTUFBTSxDQUFDcUIsU0FBU0MsV0FBVyxHQUFHdEIsK0NBQVFBLENBQVcsRUFBRTtJQUNuRCxNQUFNLENBQUN1QixVQUFVQyxZQUFZLEdBQUd4QiwrQ0FBUUEsQ0FBa0IsRUFBRTtJQUM1RCxNQUFNLENBQUN5QixPQUFPQyxTQUFTLEdBQUcxQiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRCxNQUFNLENBQUMyQixRQUFRQyxVQUFVLEdBQUc1QiwrQ0FBUUEsQ0FBVSxFQUFFO0lBQ2hELE1BQU0sQ0FBQzZCLFVBQVVDLFlBQVksR0FBRzlCLCtDQUFRQSxDQUFpQixFQUFFO0lBRTNELGlCQUFpQjtJQUNqQixNQUFNLENBQUMrQixlQUFlQyxpQkFBaUIsR0FBR2hDLCtDQUFRQSxDQUFpQjtRQUNqRTtZQUNFaUMsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLGFBQWE7WUFDYkMsYUFBYTtZQUNiQyxpQkFBaUIsSUFBSW5CO1lBQ3JCb0IsYUFBYTtRQUNmO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLGFBQWE7WUFDYkMsYUFBYTtZQUNiQyxpQkFBaUJ2QixxSEFBT0EsQ0FBQyxJQUFJSSxRQUFRLENBQUM7WUFDdENvQixhQUFhO1FBQ2Y7S0FDRDtJQUVELG1EQUFtRDtJQUNuRHJDLGdEQUFTQTt1Q0FBQztZQUNSLE1BQU1zQyxRQUFRQztxREFBWTtvQkFDeEJ2QixlQUFlLElBQUlDO2dCQUNyQjtvREFBRztZQUVIOytDQUFPLElBQU11QixjQUFjRjs7UUFDN0I7c0NBQUcsRUFBRTtJQUVMLGdFQUFnRTtJQUNoRXRDLGdEQUFTQTt1Q0FBQztZQUNSLCtFQUErRTtZQUMvRSxNQUFNeUMsb0JBQWtDO2dCQUN0QztvQkFDRVQsSUFBSTtvQkFDSlUsT0FBTztvQkFDUEMsU0FBUztvQkFDVEMsU0FBUyxJQUFJM0IsS0FBSztvQkFDbEI0QixhQUFhO29CQUNiQyxVQUFVO29CQUNWQyxRQUFRO29CQUNSQyxRQUFRO2dCQUNWO2dCQUNBO29CQUNFaEIsSUFBSTtvQkFDSlUsT0FBTztvQkFDUEMsU0FBUztvQkFDVEMsU0FBUy9CLHFIQUFPQSxDQUFDLElBQUlJLFFBQVE7b0JBQzdCNEIsYUFBYTtvQkFDYkMsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUkMsUUFBUTtnQkFDVjtnQkFDQTtvQkFDRWhCLElBQUk7b0JBQ0pVLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLFNBQVMvQixxSEFBT0EsQ0FBQyxJQUFJSSxRQUFRO29CQUM3QjRCLGFBQWE7b0JBQ2JDLFVBQVU7b0JBQ1ZDLFFBQVE7b0JBQ1JDLFFBQVE7Z0JBQ1Y7Z0JBQ0E7b0JBQ0VoQixJQUFJO29CQUNKVSxPQUFPO29CQUNQQyxTQUFTO29CQUNUQyxTQUFTL0IscUhBQU9BLENBQUMsSUFBSUksUUFBUTtvQkFDN0I0QixhQUFhO29CQUNiQyxVQUFVO29CQUNWQyxRQUFRO29CQUNSQyxRQUFRO2dCQUNWO2dCQUNBO29CQUNFaEIsSUFBSTtvQkFDSlUsT0FBTztvQkFDUEMsU0FBUztvQkFDVEMsU0FBUyxJQUFJM0IsS0FBSztvQkFDbEI0QixhQUFhO29CQUNiQyxVQUFVO29CQUNWQyxRQUFRO29CQUNSQyxRQUFRO2dCQUNWO2dCQUNBO29CQUNFaEIsSUFBSTtvQkFDSlUsT0FBTztvQkFDUEMsU0FBUztvQkFDVEMsU0FBUy9CLHFIQUFPQSxDQUFDLElBQUlJLFFBQVE7b0JBQzdCNEIsYUFBYTtvQkFDYkMsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUkMsUUFBUTtnQkFDVjtnQkFDQTtvQkFDRWhCLElBQUk7b0JBQ0pVLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLFNBQVMsSUFBSTNCLEtBQUs7b0JBQ2xCNEIsYUFBYTtvQkFDYkMsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUkMsUUFBUTtnQkFDVjtnQkFDQTtvQkFDRWhCLElBQUk7b0JBQ0pVLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLFNBQVMsSUFBSTNCLEtBQUs7b0JBQ2xCNEIsYUFBYTtvQkFDYkMsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUkMsUUFBUTtnQkFDVjtnQkFDQTtvQkFDRWhCLElBQUk7b0JBQ0pVLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLFNBQVMsSUFBSTNCLEtBQUs7b0JBQ2xCNEIsYUFBYTtvQkFDYkMsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUkMsUUFBUTtnQkFDVjthQUNEO1lBRUQsTUFBTUMsZ0JBQTBCO2dCQUM5QjtvQkFDRWpCLElBQUk7b0JBQ0pVLE9BQU87b0JBQ1BRLFNBQVM7b0JBQ1RDLE1BQU0sSUFBSWxDO29CQUNWK0IsUUFBUTtvQkFDUkYsVUFBVTtnQkFDWjtnQkFDQTtvQkFDRWQsSUFBSTtvQkFDSlUsT0FBTztvQkFDUFEsU0FBUztvQkFDVEMsTUFBTSxJQUFJbEM7b0JBQ1YrQixRQUFRO29CQUNSRixVQUFVO2dCQUNaO2dCQUNBO29CQUNFZCxJQUFJO29CQUNKVSxPQUFPO29CQUNQUSxTQUFTO29CQUNUQyxNQUFNLElBQUlsQztvQkFDVitCLFFBQVE7b0JBQ1JGLFVBQVU7Z0JBQ1o7Z0JBQ0E7b0JBQ0VkLElBQUk7b0JBQ0pVLE9BQU87b0JBQ1BRLFNBQVM7b0JBQ1RDLE1BQU0sSUFBSWxDO29CQUNWK0IsUUFBUTtvQkFDUkYsVUFBVTtnQkFDWjthQUNEO1lBRUQsd0NBQXdDO1lBQ3hDLE1BQU1NLGlCQUFrQztnQkFDdEMsU0FBUztnQkFDVDtvQkFDRXBCLElBQUk7b0JBQ0pXLFNBQVM7b0JBQ1RVLFlBQVk7b0JBQ1pDLFNBQVM7b0JBQ1RDLFNBQVM7b0JBQ1RDLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLEtBQUs7b0JBQ0xDLFVBQVU7Z0JBQ1o7Z0JBQ0E7b0JBQ0UzQixJQUFJO29CQUNKVyxTQUFTO29CQUNUVSxZQUFZO29CQUNaQyxTQUFTO29CQUNUQyxTQUFTO29CQUNUQyxNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxLQUFLO29CQUNMQyxVQUFVO2dCQUNaO2dCQUVBLFVBQVU7Z0JBQ1Y7b0JBQ0UzQixJQUFJO29CQUNKVyxTQUFTO29CQUNUVSxZQUFZO29CQUNaQyxTQUFTO29CQUNUQyxTQUFTO29CQUNUQyxNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxLQUFLO29CQUNMQyxVQUFVO2dCQUNaO2dCQUNBO29CQUNFM0IsSUFBSTtvQkFDSlcsU0FBUztvQkFDVFUsWUFBWTtvQkFDWkMsU0FBUztvQkFDVEMsU0FBUztvQkFDVEMsTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsS0FBSztvQkFDTEMsVUFBVTtnQkFDWjtnQkFFQSxZQUFZO2dCQUNaO29CQUNFM0IsSUFBSTtvQkFDSlcsU0FBUztvQkFDVFUsWUFBWTtvQkFDWkMsU0FBUztvQkFDVEMsU0FBUztvQkFDVEMsTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsS0FBSztvQkFDTEMsVUFBVTtnQkFDWjtnQkFDQTtvQkFDRTNCLElBQUk7b0JBQ0pXLFNBQVM7b0JBQ1RVLFlBQVk7b0JBQ1pDLFNBQVM7b0JBQ1RDLFNBQVM7b0JBQ1RDLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLEtBQUs7b0JBQ0xDLFVBQVU7Z0JBQ1o7Z0JBRUEsV0FBVztnQkFDWDtvQkFDRTNCLElBQUk7b0JBQ0pXLFNBQVM7b0JBQ1RVLFlBQVk7b0JBQ1pDLFNBQVM7b0JBQ1RDLFNBQVM7b0JBQ1RDLE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLEtBQUs7b0JBQ0xDLFVBQVU7Z0JBQ1o7Z0JBQ0E7b0JBQ0UzQixJQUFJO29CQUNKVyxTQUFTO29CQUNUVSxZQUFZO29CQUNaQyxTQUFTO29CQUNUQyxTQUFTO29CQUNUQyxNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxLQUFLO29CQUNMQyxVQUFVO2dCQUNaO2dCQUVBLFdBQVc7Z0JBQ1g7b0JBQ0UzQixJQUFJO29CQUNKVyxTQUFTO29CQUNUVSxZQUFZO29CQUNaQyxTQUFTO29CQUNUQyxTQUFTO29CQUNUQyxNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxLQUFLO29CQUNMQyxVQUFVO2dCQUNaO2dCQUNBO29CQUNFM0IsSUFBSTtvQkFDSlcsU0FBUztvQkFDVFUsWUFBWTtvQkFDWkMsU0FBUztvQkFDVEMsU0FBUztvQkFDVEMsTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsS0FBSztvQkFDTEMsVUFBVTtnQkFDWjthQUNEO1lBRUQsdUNBQXVDO1lBQ3ZDLE1BQU1DLGNBQThCO2dCQUNsQyxpQkFBaUI7Z0JBQ2pCO29CQUFFNUIsSUFBSTtvQkFBS21CLE1BQU07b0JBQWNLLE1BQU07b0JBQXFCSyxVQUFVO29CQUFPUixZQUFZO29CQUFVUyxZQUFZO2dCQUFjO2dCQUMzSDtvQkFBRTlCLElBQUk7b0JBQUttQixNQUFNO29CQUFjSyxNQUFNO29CQUFxQkssVUFBVTtvQkFBT1IsWUFBWTtvQkFBVVMsWUFBWTtnQkFBb0I7Z0JBQ2pJO29CQUFFOUIsSUFBSTtvQkFBS21CLE1BQU07b0JBQWNLLE1BQU07b0JBQXNCSyxVQUFVO29CQUFPUixZQUFZO29CQUFVUyxZQUFZO2dCQUF3QjtnQkFDdEk7b0JBQUU5QixJQUFJO29CQUFLbUIsTUFBTTtvQkFBY0ssTUFBTTtvQkFBc0JLLFVBQVU7b0JBQU9SLFlBQVk7b0JBQVVTLFlBQVk7Z0JBQWtDO2dCQUVoSixjQUFjO2dCQUNkO29CQUFFOUIsSUFBSTtvQkFBS21CLE1BQU07b0JBQWNLLE1BQU07b0JBQXFCSyxVQUFVO29CQUFTUixZQUFZO29CQUFVUyxZQUFZO2dCQUFjO2dCQUM3SDtvQkFBRTlCLElBQUk7b0JBQUttQixNQUFNO29CQUFjSyxNQUFNO29CQUFxQkssVUFBVTtvQkFBU1IsWUFBWTtvQkFBVVMsWUFBWTtnQkFBb0I7Z0JBQ25JO29CQUFFOUIsSUFBSTtvQkFBS21CLE1BQU07b0JBQWNLLE1BQU07b0JBQXNCSyxVQUFVO29CQUFTUixZQUFZO29CQUFVUyxZQUFZO2dCQUF3QjtnQkFDeEk7b0JBQUU5QixJQUFJO29CQUFLbUIsTUFBTTtvQkFBY0ssTUFBTTtvQkFBc0JLLFVBQVU7b0JBQVNSLFlBQVk7b0JBQVVTLFlBQVk7Z0JBQWtDO2FBQ25KO1lBRUQsTUFBTUMsZUFBd0I7Z0JBQzVCO29CQUNFL0IsSUFBSTtvQkFDSlcsU0FBUztvQkFDVHFCLFFBQVE7b0JBQ1JiLE1BQU0sSUFBSWxDO29CQUNWZ0QsUUFBUTtnQkFDVjtnQkFDQTtvQkFDRWpDLElBQUk7b0JBQ0pXLFNBQVM7b0JBQ1RxQixRQUFRO29CQUNSYixNQUFNLElBQUlsQztvQkFDVmdELFFBQVE7Z0JBQ1Y7YUFDRDtZQUVELE1BQU1DLGlCQUFpQztnQkFDckM7b0JBQ0VsQyxJQUFJO29CQUNKVyxTQUFTO29CQUNUd0IsT0FBTztvQkFDUEMsTUFBTTtvQkFDTnJCLFFBQVE7Z0JBQ1Y7Z0JBQ0E7b0JBQ0VmLElBQUk7b0JBQ0pXLFNBQVM7b0JBQ1R3QixPQUFPO29CQUNQQyxNQUFNO29CQUNOckIsUUFBUTtnQkFDVjtnQkFDQTtvQkFDRWYsSUFBSTtvQkFDSlcsU0FBUztvQkFDVHdCLE9BQU87b0JBQ1BDLE1BQU07b0JBQ05yQixRQUFRO2dCQUNWO2dCQUNBO29CQUNFZixJQUFJO29CQUNKVyxTQUFTO29CQUNUd0IsT0FBTztvQkFDUEMsTUFBTTtvQkFDTnJCLFFBQVE7Z0JBQ1Y7YUFDRDtZQUVENUIsZUFBZXNCO1lBQ2ZwQixXQUFXNEI7WUFDWDFCLFlBQVk2QjtZQUNaM0IsU0FBU21DO1lBQ1RqQyxVQUFVb0M7WUFDVmxDLFlBQVlxQztRQUNkO3NDQUFHLEVBQUU7SUFFTCxNQUFNRyx1QkFBdUIsQ0FBQ3pCO1FBQzVCLE1BQU0wQixNQUFNLElBQUlyRDtRQUNoQixJQUFJTCxzSEFBUUEsQ0FBQ2dDLFNBQVMwQixNQUFNO1lBQzFCLE9BQU87UUFDVDtRQUNBLE9BQU81RCxpSUFBbUJBLENBQUNrQyxTQUFTO1lBQUUyQixXQUFXO1FBQUs7SUFDeEQ7SUFFQSxNQUFNQyxtQkFBbUIsQ0FBQzFCO1FBQ3hCLE9BQVFBO1lBQ04sS0FBSztnQkFBUSxPQUFPO1lBQ3BCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QixLQUFLO2dCQUFPLE9BQU87WUFDbkI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsTUFBTTJCLGdCQUFnQixDQUFDekI7UUFDckIsT0FBUUE7WUFDTixLQUFLO2dCQUFhLHFCQUFPLDhEQUFDekMsK0pBQWFBO29CQUFDbUUsV0FBVTs7Ozs7O1lBQ2xELEtBQUs7Z0JBQVMscUJBQU8sOERBQUNwRSwrSkFBS0E7b0JBQUNvRSxXQUFVOzs7Ozs7WUFDdEMsS0FBSztnQkFBVyxxQkFBTyw4REFBQ3BFLCtKQUFLQTtvQkFBQ29FLFdBQVU7Ozs7OztZQUN4QyxLQUFLO2dCQUFTLHFCQUFPLDhEQUFDeEUsK0pBQUlBO29CQUFDd0UsV0FBVTs7Ozs7O1lBQ3JDO2dCQUFTLHFCQUFPLDhEQUFDdkUsK0pBQUlBO29CQUFDdUUsV0FBVTs7Ozs7O1FBQ2xDO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNDOzs4Q0FDQyw4REFBQ0M7b0NBQUdGLFdBQVU7OENBQWtDOzs7Ozs7OENBQ2hELDhEQUFDRztvQ0FBRUgsV0FBVTs4Q0FBNEI7Ozs7Ozs7Ozs7OztzQ0FFM0MsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ1ozRCxZQUFZK0Qsa0JBQWtCOzs7Ozs7OENBRWpDLDhEQUFDSDtvQ0FBSUQsV0FBVTs4Q0FDWjNELFlBQVlnRSxrQkFBa0IsQ0FBQyxTQUFTO3dDQUN2Q0MsU0FBUzt3Q0FDVEMsTUFBTTt3Q0FDTkMsT0FBTzt3Q0FDUHhCLEtBQUs7b0NBQ1A7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9SLDhEQUFDaUI7Z0JBQUlELFdBQVU7O2tDQUViLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBRWIsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDdEUsK0pBQWFBO2dEQUFDc0UsV0FBVTs7Ozs7OzBEQUN6Qiw4REFBQ1M7Z0RBQUdULFdBQVU7MERBQXNDOzs7Ozs7Ozs7Ozs7a0RBRXRELDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFDWnhELFlBQVlrRSxHQUFHLENBQUMsQ0FBQ0MsMkJBQ2hCLDhEQUFDVjtnREFBd0JELFdBQVcseUJBQStELE9BQXRDRixpQkFBaUJhLFdBQVd2QyxRQUFROztrRUFDL0YsOERBQUM2Qjt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNDO2dFQUFJRCxXQUFVOztvRUFDWkQsY0FBY1ksV0FBV3JDLE1BQU07a0ZBQ2hDLDhEQUFDc0M7d0VBQUtaLFdBQVU7a0ZBQTBDVyxXQUFXMUMsT0FBTzs7Ozs7Ozs7Ozs7OzBFQUU5RSw4REFBQzJDO2dFQUFLWixXQUFVOzBFQUNiTCxxQkFBcUJnQixXQUFXekMsT0FBTzs7Ozs7Ozs7Ozs7O2tFQUc1Qyw4REFBQzJDO3dEQUFHYixXQUFVO2tFQUEwQ1csV0FBVzNDLEtBQUs7Ozs7OztrRUFDeEUsOERBQUNtQzt3REFBRUgsV0FBVTtrRUFBeUJXLFdBQVd4QyxXQUFXOzs7Ozs7OytDQVhwRHdDLFdBQVdyRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBDQWtCN0IsOERBQUMyQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ3pFLGdLQUFRQTtnREFBQ3lFLFdBQVU7Ozs7OzswREFDcEIsOERBQUNTO2dEQUFHVCxXQUFVOzBEQUFzQzs7Ozs7Ozs7Ozs7O2tEQUV0RCw4REFBQ0M7d0NBQUlELFdBQVU7OzRDQUNacEQsU0FDRWtFLE1BQU0sQ0FBQ0MsQ0FBQUEsYUFBY0EsV0FBVy9CLEdBQUcsS0FBSzNDLFlBQVlnRSxrQkFBa0IsQ0FBQyxTQUFTO29EQUFFQyxTQUFTO2dEQUFPLElBQ2xHSSxHQUFHLENBQUMsQ0FBQ0ssMkJBQ04sOERBQUNkO29EQUF3QkQsV0FBVTs4REFDakMsNEVBQUNDO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ0M7O2tGQUNDLDhEQUFDWTt3RUFBR2IsV0FBVTtrRkFBcUNlLFdBQVc5QyxPQUFPOzs7Ozs7a0ZBQ3JFLDhEQUFDa0M7d0VBQUVILFdBQVU7OzRFQUNWZSxXQUFXcEMsVUFBVTs0RUFBQzs0RUFBWW9DLFdBQVduQyxPQUFPOzs7Ozs7O2tGQUV2RCw4REFBQ3VCO3dFQUFFSCxXQUFVOzs0RUFBb0M7NEVBQVVlLFdBQVdsQyxPQUFPOzs7Ozs7Ozs7Ozs7OzBFQUUvRSw4REFBQ29CO2dFQUFJRCxXQUFVOztrRkFDYiw4REFBQ0M7d0VBQUlELFdBQVU7a0ZBQW1DZSxXQUFXakMsSUFBSTs7Ozs7O2tGQUNqRSw4REFBQ21CO3dFQUFJRCxXQUFVOzs0RUFBb0M7NEVBQU9lLFdBQVdoQyxJQUFJOzs7Ozs7O2tGQUN6RSw4REFBQ2tCO3dFQUFJRCxXQUFVO2tGQUFvQ2UsV0FBVzlCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzttREFabEU4QixXQUFXekQsRUFBRTs7Ozs7NENBaUJ4QlYsU0FBU2tFLE1BQU0sQ0FBQ0MsQ0FBQUEsYUFBY0EsV0FBVy9CLEdBQUcsS0FBSzNDLFlBQVlnRSxrQkFBa0IsQ0FBQyxTQUFTO29EQUFFQyxTQUFTO2dEQUFPLElBQUlVLE1BQU0sS0FBSyxtQkFDekgsOERBQUNmO2dEQUFJRCxXQUFVOzBEQUF5Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVNoRSw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUViLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ3ZFLCtKQUFJQTtnREFBQ3VFLFdBQVU7Ozs7OzswREFDaEIsOERBQUNTO2dEQUFHVCxXQUFVOzBEQUFzQzs7Ozs7Ozs7Ozs7O2tEQUV0RCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQ1p0RCxRQUFRZ0UsR0FBRyxDQUFDLENBQUNPLHVCQUNaLDhEQUFDaEI7Z0RBQW9CRCxXQUFXLHlCQUEyRCxPQUFsQ0YsaUJBQWlCbUIsT0FBTzdDLFFBQVE7O2tFQUN2Riw4REFBQzZCO3dEQUFJRCxXQUFVO2tFQUNiLDRFQUFDQzs0REFBSUQsV0FBVTs7Z0VBQ1pELGNBQWNrQixPQUFPM0MsTUFBTTs4RUFDNUIsOERBQUNzQztvRUFBS1osV0FBVTs4RUFDYmhFLGlJQUFtQkEsQ0FBQ2lGLE9BQU94QyxJQUFJLEVBQUU7d0VBQUVvQixXQUFXO29FQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztrRUFJMUQsOERBQUNnQjt3REFBR2IsV0FBVTtrRUFBOEJpQixPQUFPakQsS0FBSzs7Ozs7O2tFQUN4RCw4REFBQ21DO3dEQUFFSCxXQUFVO2tFQUF5QmlCLE9BQU96QyxPQUFPOzs7Ozs7OytDQVY1Q3lDLE9BQU8zRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBDQWlCekIsOERBQUMyQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ2xFLGdLQUFhQTtnREFBQ2tFLFdBQVU7Ozs7OzswREFDekIsOERBQUNTO2dEQUFHVCxXQUFVOzBEQUFzQzs7Ozs7Ozs7Ozs7O2tEQUV0RCw4REFBQ0M7d0NBQUlELFdBQVU7OzRDQUNaNUMsY0FBY3NELEdBQUcsQ0FBQyxDQUFDUSx3QkFDbEIsOERBQUNqQjtvREFBcUJELFdBQVU7O3NFQUM5Qiw4REFBQ0M7NERBQUlELFdBQVU7OzhFQUNiLDhEQUFDQztvRUFBSUQsV0FBVTs7c0ZBQ2IsOERBQUNDOzRFQUFJRCxXQUFXLDZCQUVmLE9BRENrQixRQUFRMUQsV0FBVyxHQUFHLGlCQUFpQjs7Ozs7O3NGQUV6Qyw4REFBQ3FEOzRFQUFHYixXQUFVOztnRkFBb0M7Z0ZBQUVrQixRQUFRM0QsSUFBSTs7Ozs7Ozs7Ozs7OztnRUFFakUyRCxRQUFRdkQsV0FBVyxHQUFHLG1CQUNyQiw4REFBQ2lEO29FQUFLWixXQUFVOzhFQUNia0IsUUFBUXZELFdBQVc7Ozs7Ozs7Ozs7Ozt3REFJekJ1RCxRQUFRekQsV0FBVyxrQkFDbEIsOERBQUN3Qzs7OEVBQ0MsOERBQUNFO29FQUFFSCxXQUFVOzhFQUE4QmtCLFFBQVF6RCxXQUFXOzs7Ozs7OEVBQzlELDhEQUFDMEM7b0VBQUVILFdBQVU7OEVBQ1ZrQixRQUFReEQsZUFBZSxJQUFJMUIsaUlBQW1CQSxDQUFDa0YsUUFBUXhELGVBQWUsRUFBRTt3RUFBRW1DLFdBQVc7b0VBQUs7Ozs7Ozs7Ozs7Ozs7bURBbEJ6RnFCLFFBQVE1RCxFQUFFOzs7OzswREEwQnRCLDhEQUFDNkQ7Z0RBQU9uQixXQUFVOzBEQUNoQiw0RUFBQ0M7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDakUsZ0tBQUlBOzREQUFDaUUsV0FBVTs7Ozs7O3NFQUNoQiw4REFBQ1k7NERBQUtaLFdBQVU7c0VBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVF4Qyw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUViLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ25FLCtKQUFhQTtnREFBQ21FLFdBQVU7Ozs7OzswREFDekIsOERBQUNTO2dEQUFHVCxXQUFVOzBEQUFzQzs7Ozs7Ozs7Ozs7O2tEQUV0RCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQ1psRCxNQUNFZ0UsTUFBTSxDQUFDTSxDQUFBQSxPQUFRbkYsc0hBQU9BLENBQUMsSUFBSU0sS0FBSzZFLEtBQUszQyxJQUFJLEdBQUcsSUFBSWxDLFNBQ2hEOEUsS0FBSyxDQUFDLEdBQUcsR0FDVFgsR0FBRyxDQUFDLENBQUNVLHFCQUNOLDhEQUFDbkI7Z0RBQWtCRCxXQUFXLHlCQUU3QixPQURDb0IsS0FBS2pDLFFBQVEsS0FBSyxVQUFVLDZCQUE2Qjs7a0VBRXpELDhEQUFDYzt3REFBSUQsV0FBVTs7MEVBQ2IsOERBQUNDOztrRkFDQyw4REFBQ1k7d0VBQUdiLFdBQVU7a0ZBQXlCb0IsS0FBS2hDLFVBQVU7Ozs7OztrRkFDdEQsOERBQUNlO3dFQUFFSCxXQUFVO2tGQUFxQ29CLEtBQUt6QyxVQUFVOzs7Ozs7Ozs7Ozs7MEVBRW5FLDhEQUFDaUM7Z0VBQUtaLFdBQVcsdUNBRWhCLE9BRENvQixLQUFLakMsUUFBUSxLQUFLLFVBQVUsNEJBQTRCOzBFQUV2RGlDLEtBQUtqQyxRQUFROzs7Ozs7Ozs7Ozs7a0VBR2xCLDhEQUFDZ0I7d0RBQUVILFdBQVU7a0VBQXlCb0IsS0FBSzNDLElBQUk7Ozs7OztrRUFDL0MsOERBQUMwQjt3REFBRUgsV0FBVTtrRUFBeUJvQixLQUFLdEMsSUFBSTs7Ozs7OzsrQ0FmdkNzQyxLQUFLOUQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzswQ0FzQnZCLDhEQUFDMkM7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNyRSxnS0FBV0E7Z0RBQUNxRSxXQUFVOzs7Ozs7MERBQ3ZCLDhEQUFDUztnREFBR1QsV0FBVTswREFBc0M7Ozs7Ozs7Ozs7OztrREFFdEQsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDQzt3REFBSUQsV0FBVTtrRUFBbUN4RCxZQUFZd0UsTUFBTTs7Ozs7O2tFQUNwRSw4REFBQ2Y7d0RBQUlELFdBQVU7a0VBQXVCOzs7Ozs7Ozs7Ozs7MERBRXhDLDhEQUFDQztnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFJRCxXQUFVO2tFQUNacEQsU0FBU2tFLE1BQU0sQ0FBQ1EsQ0FBQUEsSUFBS0EsRUFBRXRDLEdBQUcsS0FBSzNDLFlBQVlnRSxrQkFBa0IsQ0FBQyxTQUFTO2dFQUFFQyxTQUFTOzREQUFPLElBQUlVLE1BQU07Ozs7OztrRUFFdEcsOERBQUNmO3dEQUFJRCxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7OzBEQUV6Qyw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDQzt3REFBSUQsV0FBVTtrRUFDWmxELE1BQU1nRSxNQUFNLENBQUNTLENBQUFBLElBQUt0RixzSEFBT0EsQ0FBQyxJQUFJTSxLQUFLZ0YsRUFBRTlDLElBQUksR0FBRyxJQUFJbEMsU0FBU3lFLE1BQU07Ozs7OztrRUFFbEUsOERBQUNmO3dEQUFJRCxXQUFVO2tFQUEwQjs7Ozs7Ozs7Ozs7OzBEQUUzQyw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDQzt3REFBSUQsV0FBVTtrRUFDWjVDLGNBQWNvRSxNQUFNLENBQUMsQ0FBQ0MsT0FBT1AsVUFBWU8sUUFBUVAsUUFBUXZELFdBQVcsRUFBRTs7Ozs7O2tFQUV6RSw4REFBQ3NDO3dEQUFJRCxXQUFVO2tFQUEwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXpEO0dBM21CTTVEO0tBQUFBO0FBNm1CTixpRUFBZUEsaUJBQWlCQSxFQUFDIiwic291cmNlcyI6WyJFOlxcU2Vjb25kYXlfRGlzcGxheV9BcHBcXGFjYWRlbWljLWRhc2hib2FyZFxcc3JjXFxjb21wb25lbnRzXFxBY2FkZW1pY0Rhc2hib2FyZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENsb2NrLCBDYWxlbmRhciwgQm9va09wZW4sIE1haWwsIEJlbGwsIEFsZXJ0VHJpYW5nbGUsIENoZWNrQ2lyY2xlLCBVc2VycywgRmlsZVRleHQsIEdyYWR1YXRpb25DYXAsIE1lc3NhZ2VTcXVhcmUsIFBsdXMgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgZm9ybWF0RGlzdGFuY2VUb05vdywgaXNBZnRlciwgaXNCZWZvcmUsIGFkZERheXMgfSBmcm9tICdkYXRlLWZucyc7XG5cbi8vIFR5cGVzIGZvciBvdXIgZGF0YSBzdHJ1Y3R1cmVzXG5pbnRlcmZhY2UgQXNzaWdubWVudCB7XG4gIGlkOiBzdHJpbmc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIHN1YmplY3Q6IHN0cmluZztcbiAgZHVlRGF0ZTogRGF0ZTtcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgcHJpb3JpdHk6ICdoaWdoJyB8ICdtZWRpdW0nIHwgJ2xvdyc7XG4gIHN0YXR1czogJ3BlbmRpbmcnIHwgJ3N1Ym1pdHRlZCcgfCAnb3ZlcmR1ZSc7XG4gIHNvdXJjZTogJ2NsYXNzcm9vbScgfCAnc2xhY2snIHwgJ2Rpc2NvcmQnO1xufVxuXG5pbnRlcmZhY2UgTm90aWNlIHtcbiAgaWQ6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgY29udGVudDogc3RyaW5nO1xuICBkYXRlOiBEYXRlO1xuICBzb3VyY2U6ICdjbGFzc3Jvb20nIHwgJ3NsYWNrJyB8ICdkaXNjb3JkJyB8ICdlbWFpbCc7XG4gIHByaW9yaXR5OiAnaGlnaCcgfCAnbWVkaXVtJyB8ICdsb3cnO1xufVxuXG5pbnRlcmZhY2UgQ2xhc3NTY2hlZHVsZSB7XG4gIGlkOiBzdHJpbmc7XG4gIHN1YmplY3Q6IHN0cmluZztcbiAgY291cnNlQ29kZTogc3RyaW5nO1xuICBzZWN0aW9uOiBzdHJpbmc7XG4gIGZhY3VsdHk6IHN0cmluZztcbiAgdGltZTogc3RyaW5nO1xuICByb29tOiBzdHJpbmc7XG4gIGRheTogc3RyaW5nO1xuICBkdXJhdGlvbjogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgRXhhbVNjaGVkdWxlIHtcbiAgaWQ6IHN0cmluZztcbiAgZGF0ZTogc3RyaW5nO1xuICB0aW1lOiBzdHJpbmc7XG4gIGV4YW1UeXBlOiAnTUlEJyB8ICdGSU5BTCc7XG4gIGNvdXJzZUNvZGU6IHN0cmluZztcbiAgY291cnNlTmFtZTogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgRW1haWwge1xuICBpZDogc3RyaW5nO1xuICBzdWJqZWN0OiBzdHJpbmc7XG4gIHNlbmRlcjogc3RyaW5nO1xuICBkYXRlOiBEYXRlO1xuICBpc1JlYWQ6IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBTeWxsYWJ1c0l0ZW0ge1xuICBpZDogc3RyaW5nO1xuICBzdWJqZWN0OiBzdHJpbmc7XG4gIHRvcGljOiBzdHJpbmc7XG4gIHdlZWs6IG51bWJlcjtcbiAgc3RhdHVzOiAnY29tcGxldGVkJyB8ICdjdXJyZW50JyB8ICd1cGNvbWluZyc7XG59XG5cbmludGVyZmFjZSBTbGFja0NoYW5uZWwge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGlzQ29ubmVjdGVkOiBib29sZWFuO1xuICBsYXN0TWVzc2FnZT86IHN0cmluZztcbiAgbGFzdE1lc3NhZ2VUaW1lPzogRGF0ZTtcbiAgdW5yZWFkQ291bnQ6IG51bWJlcjtcbn1cblxuY29uc3QgQWNhZGVtaWNEYXNoYm9hcmQ6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCBbY3VycmVudFRpbWUsIHNldEN1cnJlbnRUaW1lXSA9IHVzZVN0YXRlKG5ldyBEYXRlKCkpO1xuICBjb25zdCBbYXNzaWdubWVudHMsIHNldEFzc2lnbm1lbnRzXSA9IHVzZVN0YXRlPEFzc2lnbm1lbnRbXT4oW10pO1xuICBjb25zdCBbbm90aWNlcywgc2V0Tm90aWNlc10gPSB1c2VTdGF0ZTxOb3RpY2VbXT4oW10pO1xuICBjb25zdCBbc2NoZWR1bGUsIHNldFNjaGVkdWxlXSA9IHVzZVN0YXRlPENsYXNzU2NoZWR1bGVbXT4oW10pO1xuICBjb25zdCBbZXhhbXMsIHNldEV4YW1zXSA9IHVzZVN0YXRlPEV4YW1TY2hlZHVsZVtdPihbXSk7XG4gIGNvbnN0IFtlbWFpbHMsIHNldEVtYWlsc10gPSB1c2VTdGF0ZTxFbWFpbFtdPihbXSk7XG4gIGNvbnN0IFtzeWxsYWJ1cywgc2V0U3lsbGFidXNdID0gdXNlU3RhdGU8U3lsbGFidXNJdGVtW10+KFtdKTtcblxuICAvLyBTbGFjayBDaGFubmVsc1xuICBjb25zdCBbc2xhY2tDaGFubmVscywgc2V0U2xhY2tDaGFubmVsc10gPSB1c2VTdGF0ZTxTbGFja0NoYW5uZWxbXT4oW1xuICAgIHtcbiAgICAgIGlkOiAnMScsXG4gICAgICBuYW1lOiAnY3NlLWdlbmVyYWwnLFxuICAgICAgaXNDb25uZWN0ZWQ6IHRydWUsXG4gICAgICBsYXN0TWVzc2FnZTogJ0Fzc2lnbm1lbnQgZGVhZGxpbmUgcmVtaW5kZXIgZm9yIENTRTM0MCcsXG4gICAgICBsYXN0TWVzc2FnZVRpbWU6IG5ldyBEYXRlKCksXG4gICAgICB1bnJlYWRDb3VudDogM1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICcyJyxcbiAgICAgIG5hbWU6ICdjc2UtYW5ub3VuY2VtZW50cycsXG4gICAgICBpc0Nvbm5lY3RlZDogdHJ1ZSxcbiAgICAgIGxhc3RNZXNzYWdlOiAnTWlkLXRlcm0gZXhhbSBzY2hlZHVsZSB1cGRhdGVkJyxcbiAgICAgIGxhc3RNZXNzYWdlVGltZTogYWRkRGF5cyhuZXcgRGF0ZSgpLCAtMSksXG4gICAgICB1bnJlYWRDb3VudDogMVxuICAgIH1cbiAgXSk7XG5cbiAgLy8gVXBkYXRlIHRpbWUgZXZlcnkgc2Vjb25kIGZvciByZWFsLXRpbWUgY291bnRkb3duXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBzZXRDdXJyZW50VGltZShuZXcgRGF0ZSgpKTtcbiAgICB9LCAxMDAwKTtcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKHRpbWVyKTtcbiAgfSwgW10pO1xuXG4gIC8vIE1vY2sgZGF0YSAtIEluIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgY29tZSBmcm9tIEFQSXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBTYW1wbGUgYXNzaWdubWVudHMgYmFzZWQgb24geW91ciBhY3R1YWwgY291cnNlcyB3aXRoIGF1dG8tZGV0ZWN0ZWQgZGVhZGxpbmVzXG4gICAgY29uc3Qgc2FtcGxlQXNzaWdubWVudHM6IEFzc2lnbm1lbnRbXSA9IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICcxJyxcbiAgICAgICAgdGl0bGU6ICdBc3NpZ25tZW50IDQgKENoIDUpIC0gTWVtb3J5IEhpZXJhcmNoeScsXG4gICAgICAgIHN1YmplY3Q6ICdDU0UzNDAnLFxuICAgICAgICBkdWVEYXRlOiBuZXcgRGF0ZSgnMjAyNS0wOS0wMycpLCAvLyBEdWU6IDI0LTA04oaSMDMtMDUgKGFzc3VtaW5nIFNlcHQgMylcbiAgICAgICAgZGVzY3JpcHRpb246ICdDYWNoZSBsZXZlbHMsIHBvbGljaWVzLCBkaXJlY3QgbWFwcGVkIGNhY2hlIHNpbXVsYXRpb25zJyxcbiAgICAgICAgcHJpb3JpdHk6ICdoaWdoJyxcbiAgICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICAgIHNvdXJjZTogJ2NsYXNzcm9vbSdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnMicsXG4gICAgICAgIHRpdGxlOiAnSFcgMDcgLSBDaCA1IChNZW1vcnkgJiBDYWNoZXMpJyxcbiAgICAgICAgc3ViamVjdDogJ0NTRTM0MCcsXG4gICAgICAgIGR1ZURhdGU6IGFkZERheXMobmV3IERhdGUoKSwgMiksXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTWVtb3J5IGhpZXJhcmNoeSAmIGxvY2FsaXR5IHByaW5jaXBsZXMsIGNhY2hlIGhpdC9taXNzIGNhbGN1bGF0aW9ucycsXG4gICAgICAgIHByaW9yaXR5OiAnbWVkaXVtJyxcbiAgICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICAgIHNvdXJjZTogJ2NsYXNzcm9vbSdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnMycsXG4gICAgICAgIHRpdGxlOiAnRmlsZSBTeXN0ZW1zIEltcGxlbWVudGF0aW9uIExhYicsXG4gICAgICAgIHN1YmplY3Q6ICdDU0UzMjEnLFxuICAgICAgICBkdWVEYXRlOiBhZGREYXlzKG5ldyBEYXRlKCksIDUpLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0ZTQ0sgYW5kIEpvdXJuYWxpbmcgaW1wbGVtZW50YXRpb24gLSBDaGFwdGVyIDQwICYgNDIgKE9TVEVQKScsXG4gICAgICAgIHByaW9yaXR5OiAnaGlnaCcsXG4gICAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgICBzb3VyY2U6ICdzbGFjaydcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnNCcsXG4gICAgICAgIHRpdGxlOiAnVkxTSSBEZXNpZ24gVmVyaWxvZyBQcm9qZWN0JyxcbiAgICAgICAgc3ViamVjdDogJ0NTRTQ2MCcsXG4gICAgICAgIGR1ZURhdGU6IGFkZERheXMobmV3IERhdGUoKSwgNyksXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRGlnaXRhbCBjaXJjdWl0IGRlc2lnbiB1c2luZyBWZXJpbG9nIEhETCAtIExhYiBBc3NpZ25tZW50JyxcbiAgICAgICAgcHJpb3JpdHk6ICdtZWRpdW0nLFxuICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgICAgc291cmNlOiAnZGlzY29yZCdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnNScsXG4gICAgICAgIHRpdGxlOiAnR3JhZGVkIEFzc2lnbm1lbnQgNDogQ29tbXVuaXR5IERldmNvbSBDYW1wYWlnbicsXG4gICAgICAgIHN1YmplY3Q6ICdDU1QyMDQnLFxuICAgICAgICBkdWVEYXRlOiBuZXcgRGF0ZSgnMjAyNS0wOC0yNScpLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0NvbW11bml0eSBEZXZlbG9wbWVudCBDb21tdW5pY2F0aW9uIENhbXBhaWduIHByb2plY3QnLFxuICAgICAgICBwcmlvcml0eTogJ2hpZ2gnLFxuICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgICAgc291cmNlOiAnY2xhc3Nyb29tJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICc2JyxcbiAgICAgICAgdGl0bGU6ICdUYWtlIEhvbWUgUXVpeiAzOiBJbnZlcnRlZCBQYWdlIFRhYmxlJyxcbiAgICAgICAgc3ViamVjdDogJ0NTRTMyMScsXG4gICAgICAgIGR1ZURhdGU6IGFkZERheXMobmV3IERhdGUoKSwgOSksXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTWVtb3J5IG1hbmFnZW1lbnQgY29uY2VwdHMgLSBJbnZlcnRlZCBQYWdlIFRhYmxlIGltcGxlbWVudGF0aW9uJyxcbiAgICAgICAgcHJpb3JpdHk6ICdtZWRpdW0nLFxuICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgICAgc291cmNlOiAnY2xhc3Nyb29tJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICc3JyxcbiAgICAgICAgdGl0bGU6ICdRdWl6IDQgKENoIDQpIC0gRGF0YXBhdGggJiBQaXBlbGluaW5nJyxcbiAgICAgICAgc3ViamVjdDogJ0NTRTM0MCcsXG4gICAgICAgIGR1ZURhdGU6IG5ldyBEYXRlKCcyMDI1LTA5LTA4JyksIC8vIFF1aXogNCAoQ2ggNCkg4oCTIDA4LTA1XG4gICAgICAgIGRlc2NyaXB0aW9uOiAnUGlwZWxpbmVkIGRhdGFwYXRocywgaGF6YXJkcywgYW5kIGhhemFyZCByZXNvbHV0aW9uJyxcbiAgICAgICAgcHJpb3JpdHk6ICdoaWdoJyxcbiAgICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICAgIHNvdXJjZTogJ2NsYXNzcm9vbSdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnOCcsXG4gICAgICAgIHRpdGxlOiAnQ1NUMjA0OiBGaW5hbCBQcm9qZWN0IFByZXNlbnRhdGlvbicsXG4gICAgICAgIHN1YmplY3Q6ICdDU1QyMDQnLFxuICAgICAgICBkdWVEYXRlOiBuZXcgRGF0ZSgnMjAyNS0wOS0wNCcpLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0ZpbmFsIHByZXNlbnRhdGlvbiBvbiBkZXZlbG9wbWVudCBjb21tdW5pY2F0aW9uIHByb2plY3QnLFxuICAgICAgICBwcmlvcml0eTogJ2hpZ2gnLFxuICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgICAgc291cmNlOiAnY2xhc3Nyb29tJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICc5JyxcbiAgICAgICAgdGl0bGU6ICdDU1QyMDQ6IFBvbGljeSBCcmllZiBTdWJtaXNzaW9uJyxcbiAgICAgICAgc3ViamVjdDogJ0NTVDIwNCcsXG4gICAgICAgIGR1ZURhdGU6IG5ldyBEYXRlKCcyMDI1LTA4LTMwJyksXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnUG9saWN5IGFuYWx5c2lzIGFuZCBicmllZiBvbiBjb21tdW5pY2F0aW9uIGZvciBkZXZlbG9wbWVudCcsXG4gICAgICAgIHByaW9yaXR5OiAnbWVkaXVtJyxcbiAgICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICAgIHNvdXJjZTogJ2NsYXNzcm9vbSdcbiAgICAgIH1cbiAgICBdO1xuXG4gICAgY29uc3Qgc2FtcGxlTm90aWNlczogTm90aWNlW10gPSBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnMScsXG4gICAgICAgIHRpdGxlOiAnTWlkLXRlcm0gRXhhbSBTY2hlZHVsZSBSZWxlYXNlZCcsXG4gICAgICAgIGNvbnRlbnQ6ICdDaGVjayB5b3VyIGVtYWlsIGZvciBkZXRhaWxlZCBleGFtIHRpbWV0YWJsZS4gRmlyc3QgZXhhbTogVkxTSSBEZXNpZ24gb24gSnVseSAyNicsXG4gICAgICAgIGRhdGU6IG5ldyBEYXRlKCksXG4gICAgICAgIHNvdXJjZTogJ2NsYXNzcm9vbScsXG4gICAgICAgIHByaW9yaXR5OiAnaGlnaCdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnMicsXG4gICAgICAgIHRpdGxlOiAnQ1NFMzQwOiBNZW1vcnkgSGllcmFyY2h5ICYgQ2FjaGVzIChXZWVrIDkpJyxcbiAgICAgICAgY29udGVudDogJ0N1cnJlbnQgdG9waWM6IENhY2hlIGxldmVscywgcG9saWNpZXMsIGRpcmVjdCBtYXBwZWQgc2ltdWxhdGlvbnMuIEhXIDA3IGR1ZSBzb29uLicsXG4gICAgICAgIGRhdGU6IG5ldyBEYXRlKCksXG4gICAgICAgIHNvdXJjZTogJ2NsYXNzcm9vbScsXG4gICAgICAgIHByaW9yaXR5OiAnaGlnaCdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnMycsXG4gICAgICAgIHRpdGxlOiAnQ1NUMjA0OiBDb21tdW5pdHkgRGV2Y29tIENhbXBhaWduIER1ZSBBdWcgMjUnLFxuICAgICAgICBjb250ZW50OiAnR3JhZGVkIEFzc2lnbm1lbnQgNCAtIENvbW11bml0eSBEZXZlbG9wbWVudCBDb21tdW5pY2F0aW9uIENhbXBhaWduIHByb2plY3QnLFxuICAgICAgICBkYXRlOiBuZXcgRGF0ZSgpLFxuICAgICAgICBzb3VyY2U6ICdjbGFzc3Jvb20nLFxuICAgICAgICBwcmlvcml0eTogJ2hpZ2gnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzQnLFxuICAgICAgICB0aXRsZTogJ0NTRTM0MDogUXVpeiA0IENvbWluZyBVcCAoU2VwdCA4KScsXG4gICAgICAgIGNvbnRlbnQ6ICdRdWl6IDQgb24gQ2hhcHRlciA0IC0gRGF0YXBhdGggJiBQaXBlbGluaW5nLiBSZXZpZXcgaGF6YXJkcyBhbmQgaGF6YXJkIHJlc29sdXRpb24uJyxcbiAgICAgICAgZGF0ZTogbmV3IERhdGUoKSxcbiAgICAgICAgc291cmNlOiAnY2xhc3Nyb29tJyxcbiAgICAgICAgcHJpb3JpdHk6ICdoaWdoJ1xuICAgICAgfVxuICAgIF07XG5cbiAgICAvLyBZb3VyIGV4YWN0IGNsYXNzIHNjaGVkdWxlIGFzIHByb3ZpZGVkXG4gICAgY29uc3Qgc2FtcGxlU2NoZWR1bGU6IENsYXNzU2NoZWR1bGVbXSA9IFtcbiAgICAgIC8vIFN1bmRheVxuICAgICAge1xuICAgICAgICBpZDogJzEnLFxuICAgICAgICBzdWJqZWN0OiAnQ29tcHV0ZXIgQXJjaGl0ZWN0dXJlJyxcbiAgICAgICAgY291cnNlQ29kZTogJ0NTRTM0MCcsXG4gICAgICAgIHNlY3Rpb246ICcwOCcsXG4gICAgICAgIGZhY3VsdHk6ICdQQksnLFxuICAgICAgICB0aW1lOiAnODowMCBBTSAtIDk6MjAgQU0nLFxuICAgICAgICByb29tOiAnMDlBLTA2QycsXG4gICAgICAgIGRheTogJ1N1bmRheScsXG4gICAgICAgIGR1cmF0aW9uOiAnMSBociAyMCBtaW5zJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgc3ViamVjdDogJ09wZXJhdGluZyBTeXN0ZW1zJyxcbiAgICAgICAgY291cnNlQ29kZTogJ0NTRTMyMScsXG4gICAgICAgIHNlY3Rpb246ICcxNCcsXG4gICAgICAgIGZhY3VsdHk6ICdaTUQnLFxuICAgICAgICB0aW1lOiAnMTI6MzAgUE0gLSAxOjUwIFBNJyxcbiAgICAgICAgcm9vbTogJzA5RC0xOEMnLFxuICAgICAgICBkYXk6ICdTdW5kYXknLFxuICAgICAgICBkdXJhdGlvbjogJzEgaHIgMjAgbWlucydcbiAgICAgIH0sXG5cbiAgICAgIC8vIFR1ZXNkYXlcbiAgICAgIHtcbiAgICAgICAgaWQ6ICczJyxcbiAgICAgICAgc3ViamVjdDogJ0NvbXB1dGVyIEFyY2hpdGVjdHVyZScsXG4gICAgICAgIGNvdXJzZUNvZGU6ICdDU0UzNDAnLFxuICAgICAgICBzZWN0aW9uOiAnMDgnLFxuICAgICAgICBmYWN1bHR5OiAnUEJLJyxcbiAgICAgICAgdGltZTogJzg6MDAgQU0gLSA5OjIwIEFNJyxcbiAgICAgICAgcm9vbTogJzA5QS0wNkMnLFxuICAgICAgICBkYXk6ICdUdWVzZGF5JyxcbiAgICAgICAgZHVyYXRpb246ICcxIGhyIDIwIG1pbnMnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzQnLFxuICAgICAgICBzdWJqZWN0OiAnT3BlcmF0aW5nIFN5c3RlbXMnLFxuICAgICAgICBjb3Vyc2VDb2RlOiAnQ1NFMzIxJyxcbiAgICAgICAgc2VjdGlvbjogJzE0JyxcbiAgICAgICAgZmFjdWx0eTogJ1pNRCcsXG4gICAgICAgIHRpbWU6ICcxMjozMCBQTSAtIDE6NTAgUE0nLFxuICAgICAgICByb29tOiAnMDlELTE4QycsXG4gICAgICAgIGRheTogJ1R1ZXNkYXknLFxuICAgICAgICBkdXJhdGlvbjogJzEgaHIgMjAgbWlucydcbiAgICAgIH0sXG5cbiAgICAgIC8vIFdlZG5lc2RheVxuICAgICAge1xuICAgICAgICBpZDogJzUnLFxuICAgICAgICBzdWJqZWN0OiAnVkxTSSBEZXNpZ24gTGFiJyxcbiAgICAgICAgY291cnNlQ29kZTogJ0NTRTQ2MEwnLFxuICAgICAgICBzZWN0aW9uOiAnMDYnLFxuICAgICAgICBmYWN1bHR5OiAnVEJBJyxcbiAgICAgICAgdGltZTogJzg6MDAgQU0gLSAxMDo1MCBBTScsXG4gICAgICAgIHJvb206ICcwOUYtMjRMJyxcbiAgICAgICAgZGF5OiAnV2VkbmVzZGF5JyxcbiAgICAgICAgZHVyYXRpb246ICcyIGhycyA1MCBtaW5zJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICc2JyxcbiAgICAgICAgc3ViamVjdDogJ09wZXJhdGluZyBTeXN0ZW1zIExhYicsXG4gICAgICAgIGNvdXJzZUNvZGU6ICdDU0UzMjFMJyxcbiAgICAgICAgc2VjdGlvbjogJzE0JyxcbiAgICAgICAgZmFjdWx0eTogJ1RCQScsXG4gICAgICAgIHRpbWU6ICcxMTowMCBBTSAtIDE6NTAgUE0nLFxuICAgICAgICByb29tOiAnMDlGLTI1TCcsXG4gICAgICAgIGRheTogJ1dlZG5lc2RheScsXG4gICAgICAgIGR1cmF0aW9uOiAnMiBocnMgNTAgbWlucydcbiAgICAgIH0sXG5cbiAgICAgIC8vIFRodXJzZGF5XG4gICAgICB7XG4gICAgICAgIGlkOiAnNycsXG4gICAgICAgIHN1YmplY3Q6ICdWTFNJIERlc2lnbiBUaGVvcnknLFxuICAgICAgICBjb3Vyc2VDb2RlOiAnQ1NFNDYwJyxcbiAgICAgICAgc2VjdGlvbjogJzA2JyxcbiAgICAgICAgZmFjdWx0eTogJ1lBUCcsXG4gICAgICAgIHRpbWU6ICc4OjAwIEFNIC0gMTA6NTAgQU0nLFxuICAgICAgICByb29tOiAnMDlELTE3QycsXG4gICAgICAgIGRheTogJ1RodXJzZGF5JyxcbiAgICAgICAgZHVyYXRpb246ICcyIGhycyA1MCBtaW5zJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICc4JyxcbiAgICAgICAgc3ViamVjdDogJ0NvbW11bmljYXRpb24gZm9yIFNvY2lhbCBDaGFuZ2UnLFxuICAgICAgICBjb3Vyc2VDb2RlOiAnQ1NUMjA0JyxcbiAgICAgICAgc2VjdGlvbjogJzAxJyxcbiAgICAgICAgZmFjdWx0eTogJ01GQycsXG4gICAgICAgIHRpbWU6ICcxMTowMCBBTSAtIDE6NTAgUE0nLFxuICAgICAgICByb29tOiAnMDlFLTIzQycsXG4gICAgICAgIGRheTogJ1RodXJzZGF5JyxcbiAgICAgICAgZHVyYXRpb246ICcyIGhycyA1MCBtaW5zJ1xuICAgICAgfSxcblxuICAgICAgLy8gU2F0dXJkYXlcbiAgICAgIHtcbiAgICAgICAgaWQ6ICc5JyxcbiAgICAgICAgc3ViamVjdDogJ1ZMU0kgRGVzaWduIFRoZW9yeScsXG4gICAgICAgIGNvdXJzZUNvZGU6ICdDU0U0NjAnLFxuICAgICAgICBzZWN0aW9uOiAnMDYnLFxuICAgICAgICBmYWN1bHR5OiAnWUFQJyxcbiAgICAgICAgdGltZTogJzg6MDAgQU0gLSAxMDo1MCBBTScsXG4gICAgICAgIHJvb206ICcwOUQtMTdDJyxcbiAgICAgICAgZGF5OiAnU2F0dXJkYXknLFxuICAgICAgICBkdXJhdGlvbjogJzIgaHJzIDUwIG1pbnMnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzEwJyxcbiAgICAgICAgc3ViamVjdDogJ0NvbW11bmljYXRpb24gZm9yIFNvY2lhbCBDaGFuZ2UnLFxuICAgICAgICBjb3Vyc2VDb2RlOiAnQ1NUMjA0JyxcbiAgICAgICAgc2VjdGlvbjogJzAxJyxcbiAgICAgICAgZmFjdWx0eTogJ01GQycsXG4gICAgICAgIHRpbWU6ICcxMTowMCBBTSAtIDE6NTAgUE0nLFxuICAgICAgICByb29tOiAnMDlFLTIzQycsXG4gICAgICAgIGRheTogJ1NhdHVyZGF5JyxcbiAgICAgICAgZHVyYXRpb246ICcyIGhycyA1MCBtaW5zJ1xuICAgICAgfSxcbiAgICBdO1xuXG4gICAgLy8gWW91ciBleGFjdCBleGFtIHNjaGVkdWxlIGFzIHByb3ZpZGVkXG4gICAgY29uc3Qgc2FtcGxlRXhhbXM6IEV4YW1TY2hlZHVsZVtdID0gW1xuICAgICAgLy8gTUlEIFRFUk0gRVhBTVNcbiAgICAgIHsgaWQ6ICcxJywgZGF0ZTogJzIwMjUtMDctMjYnLCB0aW1lOiAnNDozMCBQTSAtIDY6MzAgUE0nLCBleGFtVHlwZTogJ01JRCcsIGNvdXJzZUNvZGU6ICdDU0U0NjAnLCBjb3Vyc2VOYW1lOiAnVkxTSSBEZXNpZ24nIH0sXG4gICAgICB7IGlkOiAnMicsIGRhdGU6ICcyMDI1LTA3LTMxJywgdGltZTogJzI6MDAgUE0gLSA0OjAwIFBNJywgZXhhbVR5cGU6ICdNSUQnLCBjb3Vyc2VDb2RlOiAnQ1NFMzIxJywgY291cnNlTmFtZTogJ09wZXJhdGluZyBTeXN0ZW1zJyB9LFxuICAgICAgeyBpZDogJzMnLCBkYXRlOiAnMjAyNS0wOC0wMicsIHRpbWU6ICcxMTowMCBBTSAtIDE6MDAgUE0nLCBleGFtVHlwZTogJ01JRCcsIGNvdXJzZUNvZGU6ICdDU0UzNDAnLCBjb3Vyc2VOYW1lOiAnQ29tcHV0ZXIgQXJjaGl0ZWN0dXJlJyB9LFxuICAgICAgeyBpZDogJzQnLCBkYXRlOiAnMjAyNS0wOC0wMycsIHRpbWU6ICcxMTowMCBBTSAtIDE6MDAgUE0nLCBleGFtVHlwZTogJ01JRCcsIGNvdXJzZUNvZGU6ICdDU1QyMDQnLCBjb3Vyc2VOYW1lOiAnQ29tbXVuaWNhdGlvbiBmb3IgU29jaWFsIENoYW5nZScgfSxcblxuICAgICAgLy8gRklOQUwgRVhBTVNcbiAgICAgIHsgaWQ6ICc1JywgZGF0ZTogJzIwMjUtMDktMTQnLCB0aW1lOiAnNDozMCBQTSAtIDY6MzAgUE0nLCBleGFtVHlwZTogJ0ZJTkFMJywgY291cnNlQ29kZTogJ0NTRTQ2MCcsIGNvdXJzZU5hbWU6ICdWTFNJIERlc2lnbicgfSxcbiAgICAgIHsgaWQ6ICc2JywgZGF0ZTogJzIwMjUtMDktMTknLCB0aW1lOiAnMjowMCBQTSAtIDQ6MDAgUE0nLCBleGFtVHlwZTogJ0ZJTkFMJywgY291cnNlQ29kZTogJ0NTRTMyMScsIGNvdXJzZU5hbWU6ICdPcGVyYXRpbmcgU3lzdGVtcycgfSxcbiAgICAgIHsgaWQ6ICc3JywgZGF0ZTogJzIwMjUtMDktMjAnLCB0aW1lOiAnMTE6MDAgQU0gLSAxOjAwIFBNJywgZXhhbVR5cGU6ICdGSU5BTCcsIGNvdXJzZUNvZGU6ICdDU0UzNDAnLCBjb3Vyc2VOYW1lOiAnQ29tcHV0ZXIgQXJjaGl0ZWN0dXJlJyB9LFxuICAgICAgeyBpZDogJzgnLCBkYXRlOiAnMjAyNS0wOS0yMScsIHRpbWU6ICcxMTowMCBBTSAtIDE6MDAgUE0nLCBleGFtVHlwZTogJ0ZJTkFMJywgY291cnNlQ29kZTogJ0NTVDIwNCcsIGNvdXJzZU5hbWU6ICdDb21tdW5pY2F0aW9uIGZvciBTb2NpYWwgQ2hhbmdlJyB9LFxuICAgIF07XG5cbiAgICBjb25zdCBzYW1wbGVFbWFpbHM6IEVtYWlsW10gPSBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnMScsXG4gICAgICAgIHN1YmplY3Q6ICdBc3NpZ25tZW50IFN1Ym1pc3Npb24gUmVtaW5kZXInLFxuICAgICAgICBzZW5kZXI6ICdwcm9mLnNtaXRoQHVuaXZlcnNpdHkuZWR1JyxcbiAgICAgICAgZGF0ZTogbmV3IERhdGUoKSxcbiAgICAgICAgaXNSZWFkOiBmYWxzZVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgc3ViamVjdDogJ0NvdXJzZSBNYXRlcmlhbCBVcGRhdGVkJyxcbiAgICAgICAgc2VuZGVyOiAnY291cnNlLXVwZGF0ZXNAdW5pdmVyc2l0eS5lZHUnLFxuICAgICAgICBkYXRlOiBuZXcgRGF0ZSgpLFxuICAgICAgICBpc1JlYWQ6IGZhbHNlXG4gICAgICB9XG4gICAgXTtcblxuICAgIGNvbnN0IHNhbXBsZVN5bGxhYnVzOiBTeWxsYWJ1c0l0ZW1bXSA9IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICcxJyxcbiAgICAgICAgc3ViamVjdDogJ0NTRTM0MCcsXG4gICAgICAgIHRvcGljOiAnQ29tcHV0ZXIgQXJjaGl0ZWN0dXJlIC0gQ3VycmVudCBUb3BpY3MnLFxuICAgICAgICB3ZWVrOiA5LFxuICAgICAgICBzdGF0dXM6ICdjdXJyZW50J1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgc3ViamVjdDogJ0NTRTMyMScsXG4gICAgICAgIHRvcGljOiAnT3BlcmF0aW5nIFN5c3RlbXMgLSBDdXJyZW50IFRvcGljcycsXG4gICAgICAgIHdlZWs6IDgsXG4gICAgICAgIHN0YXR1czogJ2N1cnJlbnQnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzMnLFxuICAgICAgICBzdWJqZWN0OiAnQ1NFNDYwJyxcbiAgICAgICAgdG9waWM6ICdWTFNJIERlc2lnbiAtIEN1cnJlbnQgVG9waWNzJyxcbiAgICAgICAgd2VlazogOCxcbiAgICAgICAgc3RhdHVzOiAnY3VycmVudCdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnNCcsXG4gICAgICAgIHN1YmplY3Q6ICdDU1QyMDQnLFxuICAgICAgICB0b3BpYzogJ0NvbW11bmljYXRpb24gZm9yIFNvY2lhbCBDaGFuZ2UgLSBDdXJyZW50IFRvcGljcycsXG4gICAgICAgIHdlZWs6IDgsXG4gICAgICAgIHN0YXR1czogJ2N1cnJlbnQnXG4gICAgICB9XG4gICAgXTtcblxuICAgIHNldEFzc2lnbm1lbnRzKHNhbXBsZUFzc2lnbm1lbnRzKTtcbiAgICBzZXROb3RpY2VzKHNhbXBsZU5vdGljZXMpO1xuICAgIHNldFNjaGVkdWxlKHNhbXBsZVNjaGVkdWxlKTtcbiAgICBzZXRFeGFtcyhzYW1wbGVFeGFtcyk7XG4gICAgc2V0RW1haWxzKHNhbXBsZUVtYWlscyk7XG4gICAgc2V0U3lsbGFidXMoc2FtcGxlU3lsbGFidXMpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgZ2V0VGltZVVudGlsRGVhZGxpbmUgPSAoZHVlRGF0ZTogRGF0ZSkgPT4ge1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgaWYgKGlzQmVmb3JlKGR1ZURhdGUsIG5vdykpIHtcbiAgICAgIHJldHVybiAnT1ZFUkRVRSc7XG4gICAgfVxuICAgIHJldHVybiBmb3JtYXREaXN0YW5jZVRvTm93KGR1ZURhdGUsIHsgYWRkU3VmZml4OiB0cnVlIH0pO1xuICB9O1xuXG4gIGNvbnN0IGdldFByaW9yaXR5Q29sb3IgPSAocHJpb3JpdHk6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAocHJpb3JpdHkpIHtcbiAgICAgIGNhc2UgJ2hpZ2gnOiByZXR1cm4gJ3RleHQtcmVkLTYwMCBiZy1yZWQtNTAgYm9yZGVyLXJlZC0yMDAnO1xuICAgICAgY2FzZSAnbWVkaXVtJzogcmV0dXJuICd0ZXh0LXllbGxvdy02MDAgYmcteWVsbG93LTUwIGJvcmRlci15ZWxsb3ctMjAwJztcbiAgICAgIGNhc2UgJ2xvdyc6IHJldHVybiAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tNTAgYm9yZGVyLWdyZWVuLTIwMCc7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ3RleHQtZ3JheS02MDAgYmctZ3JheS01MCBib3JkZXItZ3JheS0yMDAnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRTb3VyY2VJY29uID0gKHNvdXJjZTogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzb3VyY2UpIHtcbiAgICAgIGNhc2UgJ2NsYXNzcm9vbSc6IHJldHVybiA8R3JhZHVhdGlvbkNhcCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz47XG4gICAgICBjYXNlICdzbGFjayc6IHJldHVybiA8VXNlcnMgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+O1xuICAgICAgY2FzZSAnZGlzY29yZCc6IHJldHVybiA8VXNlcnMgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+O1xuICAgICAgY2FzZSAnZW1haWwnOiByZXR1cm4gPE1haWwgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+O1xuICAgICAgZGVmYXVsdDogcmV0dXJuIDxCZWxsIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPjtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInctc2NyZWVuIGgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB0by1pbmRpZ28tMTAwIHAtMiBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtMyBtYi0yXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+QWNhZGVtaWMgRGFzaGJvYXJkPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgZm9udC1tZWRpdW1cIj5DU0UgU3R1ZGVudCBQb3J0YWw8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAge2N1cnJlbnRUaW1lLnRvTG9jYWxlVGltZVN0cmluZygpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICB7Y3VycmVudFRpbWUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgICAgICAgICAgICB3ZWVrZGF5OiAnbG9uZycsXG4gICAgICAgICAgICAgICAgeWVhcjogJ251bWVyaWMnLFxuICAgICAgICAgICAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICAgICAgICAgICAgZGF5OiAnbnVtZXJpYydcbiAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1haW4gQ29udGVudCBHcmlkIC0gT3B0aW1pemVkIGZvciAxMDI0eDYwMCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtMiBoLVtjYWxjKDEwMHZoLTEwMHB4KV1cIj5cbiAgICAgICAgey8qIExlZnQgQ29sdW1uIC0gQXNzaWdubWVudHMgJiBEZWFkbGluZXMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgey8qIEFzc2lnbm1lbnRzIHdpdGggQ291bnRkb3duICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgcC0zIGgtWzQ1dmhdIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgICAgIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1yZWQtNTAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDBcIj5VcGNvbWluZyBEZWFkbGluZXM8L2gyPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICB7YXNzaWdubWVudHMubWFwKChhc3NpZ25tZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2Fzc2lnbm1lbnQuaWR9IGNsYXNzTmFtZT17YHAtMyByb3VuZGVkLWxnIGJvcmRlciAke2dldFByaW9yaXR5Q29sb3IoYXNzaWdubWVudC5wcmlvcml0eSl9YH0+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRTb3VyY2VJY29uKGFzc2lnbm1lbnQuc291cmNlKX1cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIGZvbnQtYm9sZCB0ZXh0LWJhc2UgdGV4dC1ncmF5LTkwMFwiPnthc3NpZ25tZW50LnN1YmplY3R9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LWJvbGQgdGV4dC1yZWQtNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2dldFRpbWVVbnRpbERlYWRsaW5lKGFzc2lnbm1lbnQuZHVlRGF0ZSl9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWJhc2UgbWItMSB0ZXh0LWdyYXktOTAwXCI+e2Fzc2lnbm1lbnQudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMFwiPnthc3NpZ25tZW50LmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBUb2RheSdzIFNjaGVkdWxlICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgcC0zIGgtWzQ1dmhdIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtYmx1ZS01MDAgbXItMlwiIC8+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMFwiPlRvZGF5J3MgQ2xhc3NlczwvaDI+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtzY2hlZHVsZVxuICAgICAgICAgICAgICAgIC5maWx0ZXIoY2xhc3NfaXRlbSA9PiBjbGFzc19pdGVtLmRheSA9PT0gY3VycmVudFRpbWUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgd2Vla2RheTogJ2xvbmcnIH0pKVxuICAgICAgICAgICAgICAgIC5tYXAoKGNsYXNzX2l0ZW0pID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17Y2xhc3NfaXRlbS5pZH0gY2xhc3NOYW1lPVwicC0zIGJnLWJsdWUtNTAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWJsdWUtMzAwXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQtYmFzZSB0ZXh0LWdyYXktOTAwXCI+e2NsYXNzX2l0ZW0uc3ViamVjdH08L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTgwMCBmb250LWJvbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjbGFzc19pdGVtLmNvdXJzZUNvZGV9IC0gU2VjdGlvbiB7Y2xhc3NfaXRlbS5zZWN0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS04MDAgZm9udC1tZWRpdW1cIj5GYWN1bHR5OiB7Y2xhc3NfaXRlbS5mYWN1bHR5fTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LWJvbGQgdGV4dC1ibHVlLTgwMFwiPntjbGFzc19pdGVtLnRpbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS04MDAgZm9udC1tZWRpdW1cIj5Sb29tOiB7Y2xhc3NfaXRlbS5yb29tfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTcwMCBmb250LWJvbGRcIj57Y2xhc3NfaXRlbS5kdXJhdGlvbn08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIHtzY2hlZHVsZS5maWx0ZXIoY2xhc3NfaXRlbSA9PiBjbGFzc19pdGVtLmRheSA9PT0gY3VycmVudFRpbWUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgd2Vla2RheTogJ2xvbmcnIH0pKS5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1ncmF5LTUwMCB0ZXh0LXNtIHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgIE5vIGNsYXNzZXMgc2NoZWR1bGVkIGZvciB0b2RheVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNaWRkbGUgQ29sdW1uIC0gTm90aWNlcyAmIFN5bGxhYnVzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgIHsvKiBOb3RpY2VzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgcC0zIGgtWzQ1dmhdIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgICAgIDxCZWxsIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1vcmFuZ2UtNTAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDBcIj5SZWNlbnQgTm90aWNlczwvaDI+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIHtub3RpY2VzLm1hcCgobm90aWNlKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e25vdGljZS5pZH0gY2xhc3NOYW1lPXtgcC0zIHJvdW5kZWQtbGcgYm9yZGVyICR7Z2V0UHJpb3JpdHlDb2xvcihub3RpY2UucHJpb3JpdHkpfWB9PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Z2V0U291cmNlSWNvbihub3RpY2Uuc291cmNlKX1cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdERpc3RhbmNlVG9Ob3cobm90aWNlLmRhdGUsIHsgYWRkU3VmZml4OiB0cnVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtc20gbWItMVwiPntub3RpY2UudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMFwiPntub3RpY2UuY29udGVudH08L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2xhY2sgQ2hhbm5lbHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTMgaC1bNDV2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTNcIj5cbiAgICAgICAgICAgICAgPE1lc3NhZ2VTcXVhcmUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXB1cnBsZS01MDAgbXItMlwiIC8+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMFwiPlNsYWNrIENoYW5uZWxzPC9oMj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAge3NsYWNrQ2hhbm5lbHMubWFwKChjaGFubmVsKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2NoYW5uZWwuaWR9IGNsYXNzTmFtZT1cInAtMyBiZy1wdXJwbGUtNTAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXB1cnBsZS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTMgaC0zIHJvdW5kZWQtZnVsbCBtci0yICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBjaGFubmVsLmlzQ29ubmVjdGVkID8gJ2JnLWdyZWVuLTUwMCcgOiAnYmctcmVkLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWJhc2UgdGV4dC1ncmF5LTkwMFwiPiN7Y2hhbm5lbC5uYW1lfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICB7Y2hhbm5lbC51bnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLXJlZC01MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIGZvbnQtYm9sZCBweC0yIHB5LTEgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Y2hhbm5lbC51bnJlYWRDb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIHtjaGFubmVsLmxhc3RNZXNzYWdlICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDAgbWItMVwiPntjaGFubmVsLmxhc3RNZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjaGFubmVsLmxhc3RNZXNzYWdlVGltZSAmJiBmb3JtYXREaXN0YW5jZVRvTm93KGNoYW5uZWwubGFzdE1lc3NhZ2VUaW1lLCB7IGFkZFN1ZmZpeDogdHJ1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG5cbiAgICAgICAgICAgICAgey8qIEFkZCBDaGFubmVsIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgcC0zIGJvcmRlci0yIGJvcmRlci1kYXNoZWQgYm9yZGVyLXB1cnBsZS0zMDAgcm91bmRlZC1sZyB0ZXh0LXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTUwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+QWRkIFNsYWNrIENoYW5uZWw8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSaWdodCBDb2x1bW4gLSBFeGFtcyAmIFF1aWNrIFN0YXRzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgIHsvKiBVcGNvbWluZyBFeGFtcyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtMyBoLVs0NXZoXSBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItM1wiPlxuICAgICAgICAgICAgICA8R3JhZHVhdGlvbkNhcCBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtcmVkLTUwMCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwXCI+VXBjb21pbmcgRXhhbXM8L2gyPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICB7ZXhhbXNcbiAgICAgICAgICAgICAgICAuZmlsdGVyKGV4YW0gPT4gaXNBZnRlcihuZXcgRGF0ZShleGFtLmRhdGUpLCBuZXcgRGF0ZSgpKSlcbiAgICAgICAgICAgICAgICAuc2xpY2UoMCwgNilcbiAgICAgICAgICAgICAgICAubWFwKChleGFtKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2V4YW0uaWR9IGNsYXNzTmFtZT17YHAtMiByb3VuZGVkLWxnIGJvcmRlciAke1xuICAgICAgICAgICAgICAgICAgZXhhbS5leGFtVHlwZSA9PT0gJ0ZJTkFMJyA/ICdiZy1yZWQtNTAgYm9yZGVyLXJlZC0yMDAnIDogJ2JnLW9yYW5nZS01MCBib3JkZXItb3JhbmdlLTIwMCdcbiAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXNtXCI+e2V4YW0uY291cnNlTmFtZX08L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPntleGFtLmNvdXJzZUNvZGV9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZCBmb250LWJvbGQgJHtcbiAgICAgICAgICAgICAgICAgICAgICBleGFtLmV4YW1UeXBlID09PSAnRklOQUwnID8gJ2JnLXJlZC0yMDAgdGV4dC1yZWQtODAwJyA6ICdiZy1vcmFuZ2UtMjAwIHRleHQtb3JhbmdlLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHtleGFtLmV4YW1UeXBlfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMFwiPntleGFtLmRhdGV9PC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+e2V4YW0udGltZX08L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUXVpY2sgU3RhdHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTMgaC1bNDV2aF1cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItM1wiPlxuICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWluZGlnby01MDAgbXItMlwiIC8+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMFwiPlF1aWNrIFN0YXRzPC9oMj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIHAtMyByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItcmVkLTIwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcmVkLTYwMFwiPnthc3NpZ25tZW50cy5sZW5ndGh9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtcmVkLTYwMFwiPlBlbmRpbmcgVGFza3M8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBwLTMgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAge3NjaGVkdWxlLmZpbHRlcihjID0+IGMuZGF5ID09PSBjdXJyZW50VGltZS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywgeyB3ZWVrZGF5OiAnbG9uZycgfSkpLmxlbmd0aH1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMFwiPlRvZGF5J3MgQ2xhc3NlczwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNTAgcC0zIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1vcmFuZ2UtMjAwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1vcmFuZ2UtNjAwXCI+XG4gICAgICAgICAgICAgICAgICB7ZXhhbXMuZmlsdGVyKGUgPT4gaXNBZnRlcihuZXcgRGF0ZShlLmRhdGUpLCBuZXcgRGF0ZSgpKSkubGVuZ3RofVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW9yYW5nZS02MDBcIj5VcGNvbWluZyBFeGFtczwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNTAgcC0zIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1wdXJwbGUtMjAwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wdXJwbGUtNjAwXCI+XG4gICAgICAgICAgICAgICAgICB7c2xhY2tDaGFubmVscy5yZWR1Y2UoKHRvdGFsLCBjaGFubmVsKSA9PiB0b3RhbCArIGNoYW5uZWwudW5yZWFkQ291bnQsIDApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXB1cnBsZS02MDBcIj5TbGFjayBNZXNzYWdlczwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQWNhZGVtaWNEYXNoYm9hcmQ7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNhbGVuZGFyIiwiTWFpbCIsIkJlbGwiLCJBbGVydFRyaWFuZ2xlIiwiQ2hlY2tDaXJjbGUiLCJVc2VycyIsIkdyYWR1YXRpb25DYXAiLCJNZXNzYWdlU3F1YXJlIiwiUGx1cyIsImZvcm1hdERpc3RhbmNlVG9Ob3ciLCJpc0FmdGVyIiwiaXNCZWZvcmUiLCJhZGREYXlzIiwiQWNhZGVtaWNEYXNoYm9hcmQiLCJjdXJyZW50VGltZSIsInNldEN1cnJlbnRUaW1lIiwiRGF0ZSIsImFzc2lnbm1lbnRzIiwic2V0QXNzaWdubWVudHMiLCJub3RpY2VzIiwic2V0Tm90aWNlcyIsInNjaGVkdWxlIiwic2V0U2NoZWR1bGUiLCJleGFtcyIsInNldEV4YW1zIiwiZW1haWxzIiwic2V0RW1haWxzIiwic3lsbGFidXMiLCJzZXRTeWxsYWJ1cyIsInNsYWNrQ2hhbm5lbHMiLCJzZXRTbGFja0NoYW5uZWxzIiwiaWQiLCJuYW1lIiwiaXNDb25uZWN0ZWQiLCJsYXN0TWVzc2FnZSIsImxhc3RNZXNzYWdlVGltZSIsInVucmVhZENvdW50IiwidGltZXIiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJzYW1wbGVBc3NpZ25tZW50cyIsInRpdGxlIiwic3ViamVjdCIsImR1ZURhdGUiLCJkZXNjcmlwdGlvbiIsInByaW9yaXR5Iiwic3RhdHVzIiwic291cmNlIiwic2FtcGxlTm90aWNlcyIsImNvbnRlbnQiLCJkYXRlIiwic2FtcGxlU2NoZWR1bGUiLCJjb3Vyc2VDb2RlIiwic2VjdGlvbiIsImZhY3VsdHkiLCJ0aW1lIiwicm9vbSIsImRheSIsImR1cmF0aW9uIiwic2FtcGxlRXhhbXMiLCJleGFtVHlwZSIsImNvdXJzZU5hbWUiLCJzYW1wbGVFbWFpbHMiLCJzZW5kZXIiLCJpc1JlYWQiLCJzYW1wbGVTeWxsYWJ1cyIsInRvcGljIiwid2VlayIsImdldFRpbWVVbnRpbERlYWRsaW5lIiwibm93IiwiYWRkU3VmZml4IiwiZ2V0UHJpb3JpdHlDb2xvciIsImdldFNvdXJjZUljb24iLCJjbGFzc05hbWUiLCJkaXYiLCJoMSIsInAiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ3ZWVrZGF5IiwieWVhciIsIm1vbnRoIiwiaDIiLCJtYXAiLCJhc3NpZ25tZW50Iiwic3BhbiIsImgzIiwiZmlsdGVyIiwiY2xhc3NfaXRlbSIsImxlbmd0aCIsIm5vdGljZSIsImNoYW5uZWwiLCJidXR0b24iLCJleGFtIiwic2xpY2UiLCJjIiwiZSIsInJlZHVjZSIsInRvdGFsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AcademicDashboard.tsx\n"));

/***/ })

});