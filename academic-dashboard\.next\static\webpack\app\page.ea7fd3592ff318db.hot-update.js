"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AcademicDashboard.tsx":
/*!**********************************************!*\
  !*** ./src/components/AcademicDashboard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isBefore.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isAfter,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isAfter.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst AcademicDashboard = ()=>{\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notices, setNotices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedule, setSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exams, setExams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [emails, setEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [syllabus, setSyllabus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Update time every second for real-time countdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"AcademicDashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"AcademicDashboard.useEffect.timer\"], 1000);\n            return ({\n                \"AcademicDashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"AcademicDashboard.useEffect\"];\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    // Mock data - In real implementation, this would come from APIs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            // Sample assignments based on your actual courses with auto-detected deadlines\n            const sampleAssignments = [\n                {\n                    id: '1',\n                    title: 'Computer Architecture Assignment 3',\n                    subject: 'CSE340',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 3),\n                    description: 'CPU design and instruction set architecture analysis',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '2',\n                    title: 'Operating Systems Lab Report',\n                    subject: 'CSE321',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 5),\n                    description: 'Process scheduling and memory management implementation',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'slack'\n                },\n                {\n                    id: '3',\n                    title: 'VLSI Design Project Phase 2',\n                    subject: 'CSE460',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 7),\n                    description: 'Digital circuit design using Verilog HDL',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'discord'\n                },\n                {\n                    id: '4',\n                    title: 'Final Year Project Proposal',\n                    subject: 'CSE400',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 10),\n                    description: 'Complete project proposal and literature review',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '5',\n                    title: 'Communication Research Paper',\n                    subject: 'CST204',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 4),\n                    description: 'Social change communication strategies analysis',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                }\n            ];\n            const sampleNotices = [\n                {\n                    id: '1',\n                    title: 'Mid-term Exam Schedule Released',\n                    content: 'Check your email for detailed exam timetable',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '2',\n                    title: 'Lab Session Rescheduled',\n                    content: 'CSE 301 lab moved to Friday 2 PM',\n                    date: new Date(),\n                    source: 'slack',\n                    priority: 'medium'\n                }\n            ];\n            // Your actual class schedule\n            const sampleSchedule = [\n                // Sunday\n                {\n                    id: '1',\n                    subject: 'Programming',\n                    courseCode: 'CSE340',\n                    time: '8:30 AM - 9:20 AM',\n                    room: 'PBK-03A-OBC',\n                    day: 'Sunday'\n                },\n                // Monday\n                {\n                    id: '2',\n                    subject: 'Programming',\n                    courseCode: 'CSE340',\n                    time: '8:30 AM - 9:20 AM',\n                    room: 'PBK-03A-OBC',\n                    day: 'Monday'\n                },\n                // Tuesday\n                {\n                    id: '3',\n                    subject: 'Programming',\n                    courseCode: 'CSE340',\n                    time: '8:30 AM - 9:20 AM',\n                    room: 'PBK-03A-OBC',\n                    day: 'Tuesday'\n                },\n                // Wednesday\n                {\n                    id: '4',\n                    subject: 'Object Oriented Programming',\n                    courseCode: 'CSE340L',\n                    time: '9:00 AM - 10:50 AM',\n                    room: 'TBA-03P-24L',\n                    day: 'Wednesday'\n                },\n                {\n                    id: '5',\n                    subject: 'Algorithms',\n                    courseCode: 'CSE321L',\n                    time: '11:00 AM - 1:50 PM',\n                    room: 'TBA-03P-25L',\n                    day: 'Wednesday'\n                },\n                // Thursday\n                {\n                    id: '6',\n                    subject: 'Algorithms',\n                    courseCode: 'CSE460',\n                    time: '11:00 AM - 12:20 PM',\n                    room: '1MF-03D-17C',\n                    day: 'Thursday'\n                },\n                {\n                    id: '7',\n                    subject: 'Microprocessor',\n                    courseCode: 'CST204',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '01-MFC-09E-23C',\n                    day: 'Thursday'\n                },\n                // Friday\n                {\n                    id: '8',\n                    subject: 'Algorithms',\n                    courseCode: 'CSE460',\n                    time: '11:00 AM - 12:20 PM',\n                    room: '1AP-06D-17C',\n                    day: 'Friday'\n                },\n                {\n                    id: '9',\n                    subject: 'Microprocessor',\n                    courseCode: 'CST204',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '01-MFC-09E-23C',\n                    day: 'Friday'\n                },\n                // Saturday\n                {\n                    id: '10',\n                    subject: 'Database Lab',\n                    courseCode: 'CSE321',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '14-ZMD-03D-18C',\n                    day: 'Saturday'\n                }\n            ];\n            // Your actual exam schedule\n            const sampleExams = [\n                {\n                    id: '1',\n                    date: '2025-07-26',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE460',\n                    courseName: 'Algorithms'\n                },\n                {\n                    id: '2',\n                    date: '2025-07-31',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE321',\n                    courseName: 'Database'\n                },\n                {\n                    id: '3',\n                    date: '2025-08-02',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE340',\n                    courseName: 'Programming'\n                },\n                {\n                    id: '4',\n                    date: '2025-08-03',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CST204',\n                    courseName: 'Microprocessor'\n                },\n                {\n                    id: '5',\n                    date: '2025-08-14',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE460',\n                    courseName: 'Algorithms'\n                },\n                {\n                    id: '6',\n                    date: '2025-08-19',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE321',\n                    courseName: 'Database'\n                },\n                {\n                    id: '7',\n                    date: '2025-08-20',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE340',\n                    courseName: 'Programming'\n                },\n                {\n                    id: '8',\n                    date: '2025-08-21',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CST204',\n                    courseName: 'Microprocessor'\n                }\n            ];\n            const sampleEmails = [\n                {\n                    id: '1',\n                    subject: 'Assignment Submission Reminder',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                },\n                {\n                    id: '2',\n                    subject: 'Course Material Updated',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                }\n            ];\n            const sampleSyllabus = [\n                {\n                    id: '1',\n                    subject: 'CSE340',\n                    topic: 'Object-Oriented Programming Concepts',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '2',\n                    subject: 'CSE321',\n                    topic: 'Database Normalization & SQL',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '3',\n                    subject: 'CSE460',\n                    topic: 'Dynamic Programming Algorithms',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '4',\n                    subject: 'CST204',\n                    topic: 'Assembly Language Programming',\n                    week: 9,\n                    status: 'upcoming'\n                }\n            ];\n            setAssignments(sampleAssignments);\n            setNotices(sampleNotices);\n            setSchedule(sampleSchedule);\n            setExams(sampleExams);\n            setEmails(sampleEmails);\n            setSyllabus(sampleSyllabus);\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    const getTimeUntilDeadline = (dueDate)=>{\n        const now = new Date();\n        if ((0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__.isBefore)(dueDate, now)) {\n            return 'OVERDUE';\n        }\n        return (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(dueDate, {\n            addSuffix: true\n        });\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50 border-red-200';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n            case 'low':\n                return 'text-green-600 bg-green-50 border-green-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    const getSourceIcon = (source)=>{\n        switch(source){\n            case 'classroom':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 32\n                }, undefined);\n            case 'slack':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 28\n                }, undefined);\n            case 'discord':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 30\n                }, undefined);\n            case 'email':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 28\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4\",\n        style: {\n            width: '1024px',\n            height: '600px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-4 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-800\",\n                                    children: \"Academic Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"CSE Student Portal\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-semibold text-gray-800\",\n                                    children: currentTime.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: currentTime.toLocaleDateString('en-US', {\n                                        weekday: 'long',\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4 h-[calc(100%-120px)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Deadlines\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: assignments.map((assignment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(assignment.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    getSourceIcon(assignment.source),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 font-medium text-sm\",\n                                                                        children: assignment.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-bold\",\n                                                                children: getTimeUntilDeadline(assignment.dueDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-sm mb-1\",\n                                                        children: assignment.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: assignment.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, assignment.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-48 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Today's Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            schedule.filter((class_item)=>class_item.day === currentTime.toLocaleDateString('en-US', {\n                                                    weekday: 'long'\n                                                })).map((class_item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-blue-50 rounded-lg border border-blue-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-sm\",\n                                                                        children: class_item.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 font-medium\",\n                                                                        children: class_item.courseCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-semibold text-blue-600\",\n                                                                        children: class_item.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: class_item.room\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, class_item.id, false, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, undefined)),\n                                            schedule.filter((class_item)=>class_item.day === currentTime.toLocaleDateString('en-US', {\n                                                    weekday: 'long'\n                                                })).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500 text-sm py-4\",\n                                                children: \"No classes scheduled for today\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Recent Notices\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: notices.map((notice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(notice.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getSourceIcon(notice.source),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-xs text-gray-500\",\n                                                                    children: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(notice.date, {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-sm mb-1\",\n                                                        children: notice.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: notice.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, notice.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-48 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Current Syllabus\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: syllabus.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-50 rounded-lg border border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: item.subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: item.topic\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 font-semibold\",\n                                                                    children: [\n                                                                        \"Week \",\n                                                                        item.week\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs px-2 py-1 rounded \".concat(item.status === 'current' ? 'bg-green-200 text-green-800' : item.status === 'completed' ? 'bg-gray-200 text-gray-800' : 'bg-blue-200 text-blue-800'),\n                                                                    children: item.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, item.id, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Exams\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: exams.filter((exam)=>(0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_12__.isAfter)(new Date(exam.date), new Date())).slice(0, 6).map((exam)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg border \".concat(exam.examType === 'FINAL' ? 'bg-red-50 border-red-200' : 'bg-orange-50 border-orange-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-semibold text-sm\",\n                                                                        children: exam.courseName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-gray-700\",\n                                                                        children: exam.courseCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 439,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded font-bold \".concat(exam.examType === 'FINAL' ? 'bg-red-200 text-red-800' : 'bg-orange-200 text-orange-800'),\n                                                                children: exam.examType\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: exam.date\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: exam.time\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, exam.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-48\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 text-indigo-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-3 rounded-lg border border-red-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: assignments.length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-red-600\",\n                                                        children: \"Pending Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: schedule.filter((c)=>c.day === currentTime.toLocaleDateString('en-US', {\n                                                                weekday: 'long'\n                                                            })).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-600\",\n                                                        children: \"Today's Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 p-3 rounded-lg border border-orange-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-600\",\n                                                        children: exams.filter((e)=>(0,_barrel_optimize_names_addDays_formatDistanceToNow_isAfter_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_12__.isAfter)(new Date(e.date), new Date())).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-orange-600\",\n                                                        children: \"Upcoming Exams\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-3 rounded-lg border border-green-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: syllabus.filter((s)=>s.status === 'current').length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-600\",\n                                                        children: \"Current Topics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n        lineNumber: 278,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AcademicDashboard, \"QqYYjitf6Dwya1H9wXNNpC4jF60=\");\n_c = AcademicDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AcademicDashboard);\nvar _c;\n$RefreshReg$(_c, \"AcademicDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AcademicDashboard.tsx\n"));

/***/ })

});