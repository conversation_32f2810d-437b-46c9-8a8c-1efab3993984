"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AcademicDashboard.tsx":
/*!**********************************************!*\
  !*** ./src/components/AcademicDashboard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,BookOpen,Calendar,CheckCircle,GraduationCap,Mail,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isBefore.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,formatDistanceToNow,isBefore!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst AcademicDashboard = ()=>{\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [assignments, setAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notices, setNotices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedule, setSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exams, setExams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [emails, setEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [syllabus, setSyllabus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Update time every second for real-time countdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"AcademicDashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"AcademicDashboard.useEffect.timer\"], 1000);\n            return ({\n                \"AcademicDashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"AcademicDashboard.useEffect\"];\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    // Mock data - In real implementation, this would come from APIs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AcademicDashboard.useEffect\": ()=>{\n            // Sample assignments based on your courses with auto-detected deadlines\n            const sampleAssignments = [\n                {\n                    id: '1',\n                    title: 'Programming Assignment 4',\n                    subject: 'CSE340',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 3),\n                    description: 'Object-oriented programming concepts implementation',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'classroom'\n                },\n                {\n                    id: '2',\n                    title: 'Database Lab Report',\n                    subject: 'CSE321',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 5),\n                    description: 'Complete ER diagram and SQL queries',\n                    priority: 'high',\n                    status: 'pending',\n                    source: 'slack'\n                },\n                {\n                    id: '3',\n                    title: 'Algorithm Analysis Project',\n                    subject: 'CSE460',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 7),\n                    description: 'Time complexity analysis of sorting algorithms',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'discord'\n                },\n                {\n                    id: '4',\n                    title: 'Microprocessor Lab Assignment',\n                    subject: 'CST204',\n                    dueDate: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_2__.addDays)(new Date(), 4),\n                    description: 'Assembly language programming task',\n                    priority: 'medium',\n                    status: 'pending',\n                    source: 'classroom'\n                }\n            ];\n            const sampleNotices = [\n                {\n                    id: '1',\n                    title: 'Mid-term Exam Schedule Released',\n                    content: 'Check your email for detailed exam timetable',\n                    date: new Date(),\n                    source: 'classroom',\n                    priority: 'high'\n                },\n                {\n                    id: '2',\n                    title: 'Lab Session Rescheduled',\n                    content: 'CSE 301 lab moved to Friday 2 PM',\n                    date: new Date(),\n                    source: 'slack',\n                    priority: 'medium'\n                }\n            ];\n            // Your actual class schedule\n            const sampleSchedule = [\n                // Sunday\n                {\n                    id: '1',\n                    subject: 'Programming',\n                    courseCode: 'CSE340',\n                    time: '8:30 AM - 9:20 AM',\n                    room: 'PBK-03A-OBC',\n                    day: 'Sunday'\n                },\n                // Monday\n                {\n                    id: '2',\n                    subject: 'Programming',\n                    courseCode: 'CSE340',\n                    time: '8:30 AM - 9:20 AM',\n                    room: 'PBK-03A-OBC',\n                    day: 'Monday'\n                },\n                // Tuesday\n                {\n                    id: '3',\n                    subject: 'Programming',\n                    courseCode: 'CSE340',\n                    time: '8:30 AM - 9:20 AM',\n                    room: 'PBK-03A-OBC',\n                    day: 'Tuesday'\n                },\n                // Wednesday\n                {\n                    id: '4',\n                    subject: 'Object Oriented Programming',\n                    courseCode: 'CSE340L',\n                    time: '9:00 AM - 10:50 AM',\n                    room: 'TBA-03P-24L',\n                    day: 'Wednesday'\n                },\n                {\n                    id: '5',\n                    subject: 'Algorithms',\n                    courseCode: 'CSE321L',\n                    time: '11:00 AM - 1:50 PM',\n                    room: 'TBA-03P-25L',\n                    day: 'Wednesday'\n                },\n                // Thursday\n                {\n                    id: '6',\n                    subject: 'Algorithms',\n                    courseCode: 'CSE460',\n                    time: '11:00 AM - 12:20 PM',\n                    room: '1MF-03D-17C',\n                    day: 'Thursday'\n                },\n                {\n                    id: '7',\n                    subject: 'Microprocessor',\n                    courseCode: 'CST204',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '01-MFC-09E-23C',\n                    day: 'Thursday'\n                },\n                // Friday\n                {\n                    id: '8',\n                    subject: 'Algorithms',\n                    courseCode: 'CSE460',\n                    time: '11:00 AM - 12:20 PM',\n                    room: '1AP-06D-17C',\n                    day: 'Friday'\n                },\n                {\n                    id: '9',\n                    subject: 'Microprocessor',\n                    courseCode: 'CST204',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '01-MFC-09E-23C',\n                    day: 'Friday'\n                },\n                // Saturday\n                {\n                    id: '10',\n                    subject: 'Database Lab',\n                    courseCode: 'CSE321',\n                    time: '12:30 PM - 1:50 PM',\n                    room: '14-ZMD-03D-18C',\n                    day: 'Saturday'\n                }\n            ];\n            // Your actual exam schedule\n            const sampleExams = [\n                {\n                    id: '1',\n                    date: '2025-07-26',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE460',\n                    courseName: 'Algorithms'\n                },\n                {\n                    id: '2',\n                    date: '2025-07-31',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE321',\n                    courseName: 'Database'\n                },\n                {\n                    id: '3',\n                    date: '2025-08-02',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CSE340',\n                    courseName: 'Programming'\n                },\n                {\n                    id: '4',\n                    date: '2025-08-03',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'MID',\n                    courseCode: 'CST204',\n                    courseName: 'Microprocessor'\n                },\n                {\n                    id: '5',\n                    date: '2025-08-14',\n                    time: '4:30 PM - 6:30 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE460',\n                    courseName: 'Algorithms'\n                },\n                {\n                    id: '6',\n                    date: '2025-08-19',\n                    time: '2:00 PM - 4:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE321',\n                    courseName: 'Database'\n                },\n                {\n                    id: '7',\n                    date: '2025-08-20',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CSE340',\n                    courseName: 'Programming'\n                },\n                {\n                    id: '8',\n                    date: '2025-08-21',\n                    time: '11:00 AM - 1:00 PM',\n                    examType: 'FINAL',\n                    courseCode: 'CST204',\n                    courseName: 'Microprocessor'\n                }\n            ];\n            const sampleEmails = [\n                {\n                    id: '1',\n                    subject: 'Assignment Submission Reminder',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                },\n                {\n                    id: '2',\n                    subject: 'Course Material Updated',\n                    sender: '<EMAIL>',\n                    date: new Date(),\n                    isRead: false\n                }\n            ];\n            const sampleSyllabus = [\n                {\n                    id: '1',\n                    subject: 'CSE340',\n                    topic: 'Object-Oriented Programming Concepts',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '2',\n                    subject: 'CSE321',\n                    topic: 'Database Normalization & SQL',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '3',\n                    subject: 'CSE460',\n                    topic: 'Dynamic Programming Algorithms',\n                    week: 8,\n                    status: 'current'\n                },\n                {\n                    id: '4',\n                    subject: 'CST204',\n                    topic: 'Assembly Language Programming',\n                    week: 9,\n                    status: 'upcoming'\n                }\n            ];\n            setAssignments(sampleAssignments);\n            setNotices(sampleNotices);\n            setSchedule(sampleSchedule);\n            setExams(sampleExams);\n            setEmails(sampleEmails);\n            setSyllabus(sampleSyllabus);\n        }\n    }[\"AcademicDashboard.useEffect\"], []);\n    const getTimeUntilDeadline = (dueDate)=>{\n        const now = new Date();\n        if ((0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_3__.isBefore)(dueDate, now)) {\n            return 'OVERDUE';\n        }\n        return (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(dueDate, {\n            addSuffix: true\n        });\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'high':\n                return 'text-red-600 bg-red-50 border-red-200';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n            case 'low':\n                return 'text-green-600 bg-green-50 border-green-200';\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200';\n        }\n    };\n    const getSourceIcon = (source)=>{\n        switch(source){\n            case 'classroom':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 32\n                }, undefined);\n            case 'slack':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 28\n                }, undefined);\n            case 'discord':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 30\n                }, undefined);\n            case 'email':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 28\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4\",\n        style: {\n            width: '1024px',\n            height: '600px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-4 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-800\",\n                                    children: \"Academic Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"CSE Student Portal\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-semibold text-gray-800\",\n                                    children: currentTime.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: currentTime.toLocaleDateString('en-US', {\n                                        weekday: 'long',\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4 h-[calc(100%-120px)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Upcoming Deadlines\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: assignments.map((assignment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(assignment.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    getSourceIcon(assignment.source),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 font-medium text-sm\",\n                                                                        children: assignment.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-bold\",\n                                                                children: getTimeUntilDeadline(assignment.dueDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-sm mb-1\",\n                                                        children: assignment.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: assignment.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, assignment.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-48 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Today's Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: schedule.map((class_item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-50 rounded-lg border border-blue-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: class_item.subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: class_item.instructor\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-semibold text-blue-600\",\n                                                                    children: class_item.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: class_item.room\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, class_item.id, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Recent Notices\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: notices.map((notice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border \".concat(getPriorityColor(notice.priority)),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getSourceIcon(notice.source),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-xs text-gray-500\",\n                                                                    children: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(notice.date, {\n                                                                        addSuffix: true\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-sm mb-1\",\n                                                        children: notice.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: notice.content\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, notice.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-48 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Current Syllabus\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: syllabus.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-50 rounded-lg border border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-sm\",\n                                                                    children: item.subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: item.topic\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 font-semibold\",\n                                                                    children: [\n                                                                        \"Week \",\n                                                                        item.week\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs px-2 py-1 rounded \".concat(item.status === 'current' ? 'bg-green-200 text-green-800' : item.status === 'completed' ? 'bg-gray-200 text-gray-800' : 'bg-blue-200 text-blue-800'),\n                                                                    children: item.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, item.id, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-64 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 text-purple-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Recent Emails\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: emails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg border \".concat(email.isRead ? 'bg-gray-50 border-gray-200' : 'bg-purple-50 border-purple-200'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-sm truncate\",\n                                                                children: email.subject\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !email.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 39\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600 truncate\",\n                                                        children: email.sender\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: (0,_barrel_optimize_names_addDays_formatDistanceToNow_isBefore_date_fns__WEBPACK_IMPORTED_MODULE_4__.formatDistanceToNow)(email.date, {\n                                                            addSuffix: true\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, email.id, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-4 h-48\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_BookOpen_Calendar_CheckCircle_GraduationCap_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5 text-indigo-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-3 rounded-lg border border-red-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: assignments.length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-red-600\",\n                                                        children: \"Pending Tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-3 rounded-lg border border-blue-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: schedule.length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-600\",\n                                                        children: \"Today's Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-50 p-3 rounded-lg border border-purple-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: emails.filter((e)=>!e.isRead).length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-purple-600\",\n                                                        children: \"Unread Emails\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-3 rounded-lg border border-green-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: syllabus.filter((s)=>s.status === 'current').length\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-600\",\n                                                        children: \"Current Topics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Seconday_Display_App\\\\academic-dashboard\\\\src\\\\components\\\\AcademicDashboard.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AcademicDashboard, \"QqYYjitf6Dwya1H9wXNNpC4jF60=\");\n_c = AcademicDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AcademicDashboard);\nvar _c;\n$RefreshReg$(_c, \"AcademicDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AcademicDashboard.tsx\n"));

/***/ })

});